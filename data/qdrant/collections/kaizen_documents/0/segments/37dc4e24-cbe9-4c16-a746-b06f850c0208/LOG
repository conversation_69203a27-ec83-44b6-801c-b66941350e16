2025/05/29-10:11:35.651353 175 RocksDB version: 9.9.3
2025/05/29-10:11:35.651486 175 Compile date 2024-12-05 01:25:31
2025/05/29-10:11:35.651492 175 DB SUMMARY
2025/05/29-10:11:35.651494 175 Host name (Env):  beb888368ddf
2025/05/29-10:11:35.651496 175 DB Session ID:  Z3WRR5PFQNLDVUSNLKZV
2025/05/29-10:11:35.651543 175 SST files in ./storage/collections/kaizen_documents/0/segments/37dc4e24-cbe9-4c16-a746-b06f850c0208 dir, Total Num: 0, files: 
2025/05/29-10:11:35.651546 175 Write Ahead Log file in ./storage/collections/kaizen_documents/0/segments/37dc4e24-cbe9-4c16-a746-b06f850c0208: 
2025/05/29-10:11:35.651551 175                         Options.error_if_exists: 0
2025/05/29-10:11:35.651553 175                       Options.create_if_missing: 1
2025/05/29-10:11:35.651555 175                         Options.paranoid_checks: 1
2025/05/29-10:11:35.651559 175             Options.flush_verify_memtable_count: 1
2025/05/29-10:11:35.651561 175          Options.compaction_verify_record_count: 1
2025/05/29-10:11:35.651563 175                               Options.track_and_verify_wals_in_manifest: 0
2025/05/29-10:11:35.651566 175        Options.verify_sst_unique_id_in_manifest: 1
2025/05/29-10:11:35.651569 175                                     Options.env: 0x7be3ef622000
2025/05/29-10:11:35.651571 175                                      Options.fs: PosixFileSystem
2025/05/29-10:11:35.651573 175                                Options.info_log: 0x7be3f0ca6000
2025/05/29-10:11:35.651576 175                Options.max_file_opening_threads: 16
2025/05/29-10:11:35.651578 175                              Options.statistics: (nil)
2025/05/29-10:11:35.651581 175                               Options.use_fsync: 0
2025/05/29-10:11:35.651584 175                       Options.max_log_file_size: 1048576
2025/05/29-10:11:35.651586 175                  Options.max_manifest_file_size: 1073741824
2025/05/29-10:11:35.651588 175                   Options.log_file_time_to_roll: 0
2025/05/29-10:11:35.651589 175                       Options.keep_log_file_num: 1
2025/05/29-10:11:35.651594 175                    Options.recycle_log_file_num: 0
2025/05/29-10:11:35.651615 175                         Options.allow_fallocate: 1
2025/05/29-10:11:35.651618 175                        Options.allow_mmap_reads: 0
2025/05/29-10:11:35.651619 175                       Options.allow_mmap_writes: 0
2025/05/29-10:11:35.651624 175                        Options.use_direct_reads: 0
2025/05/29-10:11:35.651625 175                        Options.use_direct_io_for_flush_and_compaction: 0
2025/05/29-10:11:35.651628 175          Options.create_missing_column_families: 1
2025/05/29-10:11:35.651633 175                              Options.db_log_dir: 
2025/05/29-10:11:35.651637 175                                 Options.wal_dir: 
2025/05/29-10:11:35.651643 175                Options.table_cache_numshardbits: 6
2025/05/29-10:11:35.651645 175                         Options.WAL_ttl_seconds: 0
2025/05/29-10:11:35.651651 175                       Options.WAL_size_limit_MB: 0
2025/05/29-10:11:35.651653 175                        Options.max_write_batch_group_size_bytes: 1048576
2025/05/29-10:11:35.651655 175             Options.manifest_preallocation_size: 4194304
2025/05/29-10:11:35.651663 175                     Options.is_fd_close_on_exec: 1
2025/05/29-10:11:35.651665 175                   Options.advise_random_on_open: 1
2025/05/29-10:11:35.651672 175                    Options.db_write_buffer_size: 0
2025/05/29-10:11:35.651675 175                    Options.write_buffer_manager: 0x7be3f0c04e80
2025/05/29-10:11:35.651677 175           Options.random_access_max_buffer_size: 1048576
2025/05/29-10:11:35.651679 175                      Options.use_adaptive_mutex: 0
2025/05/29-10:11:35.651682 175                            Options.rate_limiter: (nil)
2025/05/29-10:11:35.651696 175     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/05/29-10:11:35.651726 175                       Options.wal_recovery_mode: 0
2025/05/29-10:11:35.651739 175                  Options.enable_thread_tracking: 0
2025/05/29-10:11:35.651742 175                  Options.enable_pipelined_write: 0
2025/05/29-10:11:35.651746 175                  Options.unordered_write: 0
2025/05/29-10:11:35.651749 175         Options.allow_concurrent_memtable_write: 1
2025/05/29-10:11:35.651753 175      Options.enable_write_thread_adaptive_yield: 1
2025/05/29-10:11:35.651759 175             Options.write_thread_max_yield_usec: 100
2025/05/29-10:11:35.651760 175            Options.write_thread_slow_yield_usec: 3
2025/05/29-10:11:35.651762 175                               Options.row_cache: None
2025/05/29-10:11:35.651764 175                              Options.wal_filter: None
2025/05/29-10:11:35.651767 175             Options.avoid_flush_during_recovery: 0
2025/05/29-10:11:35.651769 175             Options.allow_ingest_behind: 0
2025/05/29-10:11:35.651791 175             Options.two_write_queues: 0
2025/05/29-10:11:35.651798 175             Options.manual_wal_flush: 0
2025/05/29-10:11:35.651808 175             Options.wal_compression: 0
2025/05/29-10:11:35.651810 175             Options.background_close_inactive_wals: 0
2025/05/29-10:11:35.651812 175             Options.atomic_flush: 0
2025/05/29-10:11:35.651814 175             Options.avoid_unnecessary_blocking_io: 0
2025/05/29-10:11:35.651827 175             Options.prefix_seek_opt_in_only: 0
2025/05/29-10:11:35.651832 175                 Options.persist_stats_to_disk: 0
2025/05/29-10:11:35.651861 175                 Options.write_dbid_to_manifest: 1
2025/05/29-10:11:35.651867 175                 Options.write_identity_file: 1
2025/05/29-10:11:35.651869 175                 Options.log_readahead_size: 0
2025/05/29-10:11:35.651871 175                 Options.file_checksum_gen_factory: Unknown
2025/05/29-10:11:35.651875 175                 Options.best_efforts_recovery: 0
2025/05/29-10:11:35.651877 175                Options.max_bgerror_resume_count: 2147483647
2025/05/29-10:11:35.651879 175            Options.bgerror_resume_retry_interval: 1000000
2025/05/29-10:11:35.651881 175             Options.allow_data_in_errors: 0
2025/05/29-10:11:35.651882 175             Options.db_host_id: __hostname__
2025/05/29-10:11:35.651885 175             Options.enforce_single_del_contracts: true
2025/05/29-10:11:35.651886 175             Options.metadata_write_temperature: kUnknown
2025/05/29-10:11:35.651902 175             Options.wal_write_temperature: kUnknown
2025/05/29-10:11:35.651905 175             Options.max_background_jobs: 2
2025/05/29-10:11:35.651913 175             Options.max_background_compactions: -1
2025/05/29-10:11:35.651915 175             Options.max_subcompactions: 1
2025/05/29-10:11:35.651917 175             Options.avoid_flush_during_shutdown: 0
2025/05/29-10:11:35.651918 175           Options.writable_file_max_buffer_size: 1048576
2025/05/29-10:11:35.651920 175             Options.delayed_write_rate : 16777216
2025/05/29-10:11:35.651933 175             Options.max_total_wal_size: 0
2025/05/29-10:11:35.651946 175             Options.delete_obsolete_files_period_micros: 180000000
2025/05/29-10:11:35.651951 175                   Options.stats_dump_period_sec: 600
2025/05/29-10:11:35.651955 175                 Options.stats_persist_period_sec: 600
2025/05/29-10:11:35.651962 175                 Options.stats_history_buffer_size: 1048576
2025/05/29-10:11:35.651964 175                          Options.max_open_files: 256
2025/05/29-10:11:35.651966 175                          Options.bytes_per_sync: 0
2025/05/29-10:11:35.651971 175                      Options.wal_bytes_per_sync: 0
2025/05/29-10:11:35.651974 175                   Options.strict_bytes_per_sync: 0
2025/05/29-10:11:35.651975 175       Options.compaction_readahead_size: 2097152
2025/05/29-10:11:35.651980 175                  Options.max_background_flushes: -1
2025/05/29-10:11:35.651992 175 Options.daily_offpeak_time_utc: 
2025/05/29-10:11:35.651997 175 Compression algorithms supported:
2025/05/29-10:11:35.652000 175 	kZSTD supported: 0
2025/05/29-10:11:35.652026 175 	kXpressCompression supported: 0
2025/05/29-10:11:35.652028 175 	kBZip2Compression supported: 0
2025/05/29-10:11:35.652030 175 	kZSTDNotFinalCompression supported: 0
2025/05/29-10:11:35.652035 175 	kLZ4Compression supported: 1
2025/05/29-10:11:35.652037 175 	kZlibCompression supported: 0
2025/05/29-10:11:35.652038 175 	kLZ4HCCompression supported: 1
2025/05/29-10:11:35.652043 175 	kSnappyCompression supported: 1
2025/05/29-10:11:35.652046 175 Fast CRC32 supported: Not supported on x86
2025/05/29-10:11:35.652048 175 DMutex implementation: pthread_mutex_t
2025/05/29-10:11:35.652050 175 Jemalloc supported: 0
2025/05/29-10:11:35.674497 175               Options.comparator: leveldb.BytewiseComparator
2025/05/29-10:11:35.674503 175           Options.merge_operator: None
2025/05/29-10:11:35.674505 175        Options.compaction_filter: None
2025/05/29-10:11:35.674507 175        Options.compaction_filter_factory: None
2025/05/29-10:11:35.674509 175  Options.sst_partitioner_factory: None
2025/05/29-10:11:35.674511 175         Options.memtable_factory: SkipListFactory
2025/05/29-10:11:35.674513 175            Options.table_factory: BlockBasedTable
2025/05/29-10:11:35.674603 175            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7be3f0c00ce0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7be3f0c36010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/29-10:11:35.674610 175        Options.write_buffer_size: 10485760
2025/05/29-10:11:35.674613 175  Options.max_write_buffer_number: 2
2025/05/29-10:11:35.674618 175          Options.compression: LZ4
2025/05/29-10:11:35.674619 175                  Options.bottommost_compression: Disabled
2025/05/29-10:11:35.674621 175       Options.prefix_extractor: nullptr
2025/05/29-10:11:35.674624 175   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/29-10:11:35.674627 175             Options.num_levels: 7
2025/05/29-10:11:35.674630 175        Options.min_write_buffer_number_to_merge: 1
2025/05/29-10:11:35.674637 175     Options.max_write_buffer_number_to_maintain: 0
2025/05/29-10:11:35.674639 175     Options.max_write_buffer_size_to_maintain: 0
2025/05/29-10:11:35.674642 175            Options.bottommost_compression_opts.window_bits: -14
2025/05/29-10:11:35.674644 175                  Options.bottommost_compression_opts.level: 32767
2025/05/29-10:11:35.674646 175               Options.bottommost_compression_opts.strategy: 0
2025/05/29-10:11:35.674648 175         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.674650 175         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.674654 175         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/29-10:11:35.674656 175                  Options.bottommost_compression_opts.enabled: false
2025/05/29-10:11:35.674658 175         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.674660 175         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.674661 175            Options.compression_opts.window_bits: -14
2025/05/29-10:11:35.674663 175                  Options.compression_opts.level: 32767
2025/05/29-10:11:35.674665 175               Options.compression_opts.strategy: 0
2025/05/29-10:11:35.674669 175         Options.compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.674671 175         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.674674 175         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.674677 175         Options.compression_opts.parallel_threads: 1
2025/05/29-10:11:35.674679 175                  Options.compression_opts.enabled: false
2025/05/29-10:11:35.674682 175         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.674683 175      Options.level0_file_num_compaction_trigger: 4
2025/05/29-10:11:35.674685 175          Options.level0_slowdown_writes_trigger: 20
2025/05/29-10:11:35.674687 175              Options.level0_stop_writes_trigger: 36
2025/05/29-10:11:35.674689 175                   Options.target_file_size_base: 67108864
2025/05/29-10:11:35.674692 175             Options.target_file_size_multiplier: 1
2025/05/29-10:11:35.674693 175                Options.max_bytes_for_level_base: 268435456
2025/05/29-10:11:35.674695 175 Options.level_compaction_dynamic_level_bytes: 1
2025/05/29-10:11:35.674697 175          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/29-10:11:35.674699 175 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/29-10:11:35.674701 175 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/29-10:11:35.674721 175 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/29-10:11:35.674723 175 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/29-10:11:35.674727 175 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/29-10:11:35.674728 175 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/29-10:11:35.674734 175 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/29-10:11:35.674738 175       Options.max_sequential_skip_in_iterations: 8
2025/05/29-10:11:35.674739 175                    Options.max_compaction_bytes: 1677721600
2025/05/29-10:11:35.674761 175                        Options.arena_block_size: 1048576
2025/05/29-10:11:35.674763 175   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/29-10:11:35.674765 175   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/29-10:11:35.674767 175                Options.disable_auto_compactions: 0
2025/05/29-10:11:35.674771 175                        Options.compaction_style: kCompactionStyleLevel
2025/05/29-10:11:35.674774 175                          Options.compaction_pri: kMinOverlappingRatio
2025/05/29-10:11:35.674776 175 Options.compaction_options_universal.size_ratio: 1
2025/05/29-10:11:35.674777 175 Options.compaction_options_universal.min_merge_width: 2
2025/05/29-10:11:35.674779 175 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/29-10:11:35.674781 175 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/29-10:11:35.674782 175 Options.compaction_options_universal.compression_size_percent: -1
2025/05/29-10:11:35.674784 175 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/29-10:11:35.674786 175 Options.compaction_options_universal.max_read_amp: -1
2025/05/29-10:11:35.674787 175 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/29-10:11:35.674789 175 Options.compaction_options_fifo.allow_compaction: 0
2025/05/29-10:11:35.674794 175                   Options.table_properties_collectors: 
2025/05/29-10:11:35.674799 175                   Options.inplace_update_support: 0
2025/05/29-10:11:35.674800 175                 Options.inplace_update_num_locks: 10000
2025/05/29-10:11:35.674803 175               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/29-10:11:35.674805 175               Options.memtable_whole_key_filtering: 0
2025/05/29-10:11:35.674815 175   Options.memtable_huge_page_size: 0
2025/05/29-10:11:35.674819 175                           Options.bloom_locality: 0
2025/05/29-10:11:35.674820 175                    Options.max_successive_merges: 0
2025/05/29-10:11:35.674823 175             Options.strict_max_successive_merges: 0
2025/05/29-10:11:35.674828 175                Options.optimize_filters_for_hits: 0
2025/05/29-10:11:35.674830 175                Options.paranoid_file_checks: 0
2025/05/29-10:11:35.674831 175                Options.force_consistency_checks: 1
2025/05/29-10:11:35.674833 175                Options.report_bg_io_stats: 0
2025/05/29-10:11:35.674835 175                               Options.ttl: 2592000
2025/05/29-10:11:35.674838 175          Options.periodic_compaction_seconds: 0
2025/05/29-10:11:35.674841 175                        Options.default_temperature: kUnknown
2025/05/29-10:11:35.674844 175  Options.preclude_last_level_data_seconds: 0
2025/05/29-10:11:35.674852 175    Options.preserve_internal_time_seconds: 0
2025/05/29-10:11:35.674853 175                       Options.enable_blob_files: false
2025/05/29-10:11:35.674856 175                           Options.min_blob_size: 0
2025/05/29-10:11:35.674858 175                          Options.blob_file_size: 268435456
2025/05/29-10:11:35.674861 175                   Options.blob_compression_type: NoCompression
2025/05/29-10:11:35.674864 175          Options.enable_blob_garbage_collection: false
2025/05/29-10:11:35.674866 175      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/29-10:11:35.674869 175 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/29-10:11:35.674871 175          Options.blob_compaction_readahead_size: 0
2025/05/29-10:11:35.674872 175                Options.blob_file_starting_level: 0
2025/05/29-10:11:35.674874 175         Options.experimental_mempurge_threshold: 0.000000
2025/05/29-10:11:35.674886 175            Options.memtable_max_range_deletions: 0
2025/05/29-10:11:35.695027 175               Options.comparator: leveldb.BytewiseComparator
2025/05/29-10:11:35.695032 175           Options.merge_operator: None
2025/05/29-10:11:35.695034 175        Options.compaction_filter: None
2025/05/29-10:11:35.695036 175        Options.compaction_filter_factory: None
2025/05/29-10:11:35.695038 175  Options.sst_partitioner_factory: None
2025/05/29-10:11:35.695039 175         Options.memtable_factory: SkipListFactory
2025/05/29-10:11:35.695041 175            Options.table_factory: BlockBasedTable
2025/05/29-10:11:35.695067 175            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7be3f0c00ce0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7be3f0c36010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/29-10:11:35.695076 175        Options.write_buffer_size: 10485760
2025/05/29-10:11:35.695078 175  Options.max_write_buffer_number: 2
2025/05/29-10:11:35.695080 175          Options.compression: LZ4
2025/05/29-10:11:35.695082 175                  Options.bottommost_compression: Disabled
2025/05/29-10:11:35.695083 175       Options.prefix_extractor: nullptr
2025/05/29-10:11:35.695085 175   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/29-10:11:35.695089 175             Options.num_levels: 7
2025/05/29-10:11:35.695091 175        Options.min_write_buffer_number_to_merge: 1
2025/05/29-10:11:35.695092 175     Options.max_write_buffer_number_to_maintain: 0
2025/05/29-10:11:35.695094 175     Options.max_write_buffer_size_to_maintain: 0
2025/05/29-10:11:35.695096 175            Options.bottommost_compression_opts.window_bits: -14
2025/05/29-10:11:35.695097 175                  Options.bottommost_compression_opts.level: 32767
2025/05/29-10:11:35.695099 175               Options.bottommost_compression_opts.strategy: 0
2025/05/29-10:11:35.695101 175         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.695102 175         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.695104 175         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/29-10:11:35.695105 175                  Options.bottommost_compression_opts.enabled: false
2025/05/29-10:11:35.695107 175         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.695109 175         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.695110 175            Options.compression_opts.window_bits: -14
2025/05/29-10:11:35.695112 175                  Options.compression_opts.level: 32767
2025/05/29-10:11:35.695113 175               Options.compression_opts.strategy: 0
2025/05/29-10:11:35.695115 175         Options.compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.695116 175         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.695120 175         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.695122 175         Options.compression_opts.parallel_threads: 1
2025/05/29-10:11:35.695123 175                  Options.compression_opts.enabled: false
2025/05/29-10:11:35.695125 175         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.695126 175      Options.level0_file_num_compaction_trigger: 4
2025/05/29-10:11:35.695128 175          Options.level0_slowdown_writes_trigger: 20
2025/05/29-10:11:35.695130 175              Options.level0_stop_writes_trigger: 36
2025/05/29-10:11:35.695132 175                   Options.target_file_size_base: 67108864
2025/05/29-10:11:35.695133 175             Options.target_file_size_multiplier: 1
2025/05/29-10:11:35.695135 175                Options.max_bytes_for_level_base: 268435456
2025/05/29-10:11:35.695136 175 Options.level_compaction_dynamic_level_bytes: 1
2025/05/29-10:11:35.695139 175          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/29-10:11:35.695141 175 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/29-10:11:35.695142 175 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/29-10:11:35.695144 175 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/29-10:11:35.695146 175 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/29-10:11:35.695147 175 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/29-10:11:35.695148 175 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/29-10:11:35.695150 175 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/29-10:11:35.695151 175       Options.max_sequential_skip_in_iterations: 8
2025/05/29-10:11:35.695153 175                    Options.max_compaction_bytes: 1677721600
2025/05/29-10:11:35.695155 175                        Options.arena_block_size: 1048576
2025/05/29-10:11:35.695156 175   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/29-10:11:35.695158 175   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/29-10:11:35.695159 175                Options.disable_auto_compactions: 0
2025/05/29-10:11:35.695165 175                        Options.compaction_style: kCompactionStyleLevel
2025/05/29-10:11:35.695166 175                          Options.compaction_pri: kMinOverlappingRatio
2025/05/29-10:11:35.695168 175 Options.compaction_options_universal.size_ratio: 1
2025/05/29-10:11:35.695169 175 Options.compaction_options_universal.min_merge_width: 2
2025/05/29-10:11:35.695171 175 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/29-10:11:35.695173 175 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/29-10:11:35.695175 175 Options.compaction_options_universal.compression_size_percent: -1
2025/05/29-10:11:35.695176 175 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/29-10:11:35.695178 175 Options.compaction_options_universal.max_read_amp: -1
2025/05/29-10:11:35.695179 175 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/29-10:11:35.695181 175 Options.compaction_options_fifo.allow_compaction: 0
2025/05/29-10:11:35.695186 175                   Options.table_properties_collectors: 
2025/05/29-10:11:35.695187 175                   Options.inplace_update_support: 0
2025/05/29-10:11:35.695189 175                 Options.inplace_update_num_locks: 10000
2025/05/29-10:11:35.695191 175               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/29-10:11:35.695193 175               Options.memtable_whole_key_filtering: 0
2025/05/29-10:11:35.695195 175   Options.memtable_huge_page_size: 0
2025/05/29-10:11:35.695196 175                           Options.bloom_locality: 0
2025/05/29-10:11:35.695198 175                    Options.max_successive_merges: 0
2025/05/29-10:11:35.695199 175             Options.strict_max_successive_merges: 0
2025/05/29-10:11:35.695201 175                Options.optimize_filters_for_hits: 0
2025/05/29-10:11:35.695202 175                Options.paranoid_file_checks: 0
2025/05/29-10:11:35.695204 175                Options.force_consistency_checks: 1
2025/05/29-10:11:35.695205 175                Options.report_bg_io_stats: 0
2025/05/29-10:11:35.695207 175                               Options.ttl: 2592000
2025/05/29-10:11:35.695208 175          Options.periodic_compaction_seconds: 0
2025/05/29-10:11:35.695210 175                        Options.default_temperature: kUnknown
2025/05/29-10:11:35.695212 175  Options.preclude_last_level_data_seconds: 0
2025/05/29-10:11:35.695213 175    Options.preserve_internal_time_seconds: 0
2025/05/29-10:11:35.695215 175                       Options.enable_blob_files: false
2025/05/29-10:11:35.695216 175                           Options.min_blob_size: 0
2025/05/29-10:11:35.695218 175                          Options.blob_file_size: 268435456
2025/05/29-10:11:35.695220 175                   Options.blob_compression_type: NoCompression
2025/05/29-10:11:35.695221 175          Options.enable_blob_garbage_collection: false
2025/05/29-10:11:35.695223 175      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/29-10:11:35.695225 175 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/29-10:11:35.695227 175          Options.blob_compaction_readahead_size: 0
2025/05/29-10:11:35.695228 175                Options.blob_file_starting_level: 0
2025/05/29-10:11:35.695230 175         Options.experimental_mempurge_threshold: 0.000000
2025/05/29-10:11:35.695232 175            Options.memtable_max_range_deletions: 0
2025/05/29-10:11:35.699838 175               Options.comparator: leveldb.BytewiseComparator
2025/05/29-10:11:35.699844 175           Options.merge_operator: None
2025/05/29-10:11:35.699849 175        Options.compaction_filter: None
2025/05/29-10:11:35.699851 175        Options.compaction_filter_factory: None
2025/05/29-10:11:35.699852 175  Options.sst_partitioner_factory: None
2025/05/29-10:11:35.699864 175         Options.memtable_factory: SkipListFactory
2025/05/29-10:11:35.699870 175            Options.table_factory: BlockBasedTable
2025/05/29-10:11:35.699896 175            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7be3f0c00ce0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7be3f0c36010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/29-10:11:35.699901 175        Options.write_buffer_size: 10485760
2025/05/29-10:11:35.699903 175  Options.max_write_buffer_number: 2
2025/05/29-10:11:35.699905 175          Options.compression: LZ4
2025/05/29-10:11:35.699907 175                  Options.bottommost_compression: Disabled
2025/05/29-10:11:35.699909 175       Options.prefix_extractor: nullptr
2025/05/29-10:11:35.699911 175   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/29-10:11:35.699912 175             Options.num_levels: 7
2025/05/29-10:11:35.699914 175        Options.min_write_buffer_number_to_merge: 1
2025/05/29-10:11:35.699918 175     Options.max_write_buffer_number_to_maintain: 0
2025/05/29-10:11:35.699921 175     Options.max_write_buffer_size_to_maintain: 0
2025/05/29-10:11:35.699925 175            Options.bottommost_compression_opts.window_bits: -14
2025/05/29-10:11:35.699927 175                  Options.bottommost_compression_opts.level: 32767
2025/05/29-10:11:35.699929 175               Options.bottommost_compression_opts.strategy: 0
2025/05/29-10:11:35.699931 175         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.699933 175         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.699937 175         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/29-10:11:35.699938 175                  Options.bottommost_compression_opts.enabled: false
2025/05/29-10:11:35.699941 175         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.699943 175         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.699945 175            Options.compression_opts.window_bits: -14
2025/05/29-10:11:35.699948 175                  Options.compression_opts.level: 32767
2025/05/29-10:11:35.699950 175               Options.compression_opts.strategy: 0
2025/05/29-10:11:35.699952 175         Options.compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.699954 175         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.699956 175         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.699958 175         Options.compression_opts.parallel_threads: 1
2025/05/29-10:11:35.699961 175                  Options.compression_opts.enabled: false
2025/05/29-10:11:35.699976 175         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.699977 175      Options.level0_file_num_compaction_trigger: 4
2025/05/29-10:11:35.699979 175          Options.level0_slowdown_writes_trigger: 20
2025/05/29-10:11:35.699981 175              Options.level0_stop_writes_trigger: 36
2025/05/29-10:11:35.699983 175                   Options.target_file_size_base: 67108864
2025/05/29-10:11:35.699984 175             Options.target_file_size_multiplier: 1
2025/05/29-10:11:35.699986 175                Options.max_bytes_for_level_base: 268435456
2025/05/29-10:11:35.699989 175 Options.level_compaction_dynamic_level_bytes: 1
2025/05/29-10:11:35.699994 175          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/29-10:11:35.699996 175 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/29-10:11:35.699998 175 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/29-10:11:35.700002 175 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/29-10:11:35.700004 175 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/29-10:11:35.700005 175 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/29-10:11:35.700007 175 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/29-10:11:35.700009 175 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/29-10:11:35.700011 175       Options.max_sequential_skip_in_iterations: 8
2025/05/29-10:11:35.700012 175                    Options.max_compaction_bytes: 1677721600
2025/05/29-10:11:35.700017 175                        Options.arena_block_size: 1048576
2025/05/29-10:11:35.700019 175   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/29-10:11:35.700021 175   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/29-10:11:35.700023 175                Options.disable_auto_compactions: 0
2025/05/29-10:11:35.700025 175                        Options.compaction_style: kCompactionStyleLevel
2025/05/29-10:11:35.700027 175                          Options.compaction_pri: kMinOverlappingRatio
2025/05/29-10:11:35.700029 175 Options.compaction_options_universal.size_ratio: 1
2025/05/29-10:11:35.700031 175 Options.compaction_options_universal.min_merge_width: 2
2025/05/29-10:11:35.700032 175 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/29-10:11:35.700034 175 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/29-10:11:35.700036 175 Options.compaction_options_universal.compression_size_percent: -1
2025/05/29-10:11:35.700039 175 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/29-10:11:35.700041 175 Options.compaction_options_universal.max_read_amp: -1
2025/05/29-10:11:35.700042 175 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/29-10:11:35.700044 175 Options.compaction_options_fifo.allow_compaction: 0
2025/05/29-10:11:35.700049 175                   Options.table_properties_collectors: 
2025/05/29-10:11:35.700050 175                   Options.inplace_update_support: 0
2025/05/29-10:11:35.700052 175                 Options.inplace_update_num_locks: 10000
2025/05/29-10:11:35.700054 175               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/29-10:11:35.700056 175               Options.memtable_whole_key_filtering: 0
2025/05/29-10:11:35.700058 175   Options.memtable_huge_page_size: 0
2025/05/29-10:11:35.700059 175                           Options.bloom_locality: 0
2025/05/29-10:11:35.700061 175                    Options.max_successive_merges: 0
2025/05/29-10:11:35.700063 175             Options.strict_max_successive_merges: 0
2025/05/29-10:11:35.700066 175                Options.optimize_filters_for_hits: 0
2025/05/29-10:11:35.700070 175                Options.paranoid_file_checks: 0
2025/05/29-10:11:35.700072 175                Options.force_consistency_checks: 1
2025/05/29-10:11:35.700074 175                Options.report_bg_io_stats: 0
2025/05/29-10:11:35.700075 175                               Options.ttl: 2592000
2025/05/29-10:11:35.700077 175          Options.periodic_compaction_seconds: 0
2025/05/29-10:11:35.700079 175                        Options.default_temperature: kUnknown
2025/05/29-10:11:35.700081 175  Options.preclude_last_level_data_seconds: 0
2025/05/29-10:11:35.700083 175    Options.preserve_internal_time_seconds: 0
2025/05/29-10:11:35.700084 175                       Options.enable_blob_files: false
2025/05/29-10:11:35.700086 175                           Options.min_blob_size: 0
2025/05/29-10:11:35.700087 175                          Options.blob_file_size: 268435456
2025/05/29-10:11:35.700089 175                   Options.blob_compression_type: NoCompression
2025/05/29-10:11:35.700090 175          Options.enable_blob_garbage_collection: false
2025/05/29-10:11:35.700092 175      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/29-10:11:35.700094 175 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/29-10:11:35.700096 175          Options.blob_compaction_readahead_size: 0
2025/05/29-10:11:35.700097 175                Options.blob_file_starting_level: 0
2025/05/29-10:11:35.700103 175         Options.experimental_mempurge_threshold: 0.000000
2025/05/29-10:11:35.700105 175            Options.memtable_max_range_deletions: 0
2025/05/29-10:11:35.704530 175               Options.comparator: leveldb.BytewiseComparator
2025/05/29-10:11:35.704535 175           Options.merge_operator: None
2025/05/29-10:11:35.704537 175        Options.compaction_filter: None
2025/05/29-10:11:35.704539 175        Options.compaction_filter_factory: None
2025/05/29-10:11:35.704543 175  Options.sst_partitioner_factory: None
2025/05/29-10:11:35.704545 175         Options.memtable_factory: SkipListFactory
2025/05/29-10:11:35.704547 175            Options.table_factory: BlockBasedTable
2025/05/29-10:11:35.704573 175            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7be3f0c00ce0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7be3f0c36010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/29-10:11:35.704578 175        Options.write_buffer_size: 10485760
2025/05/29-10:11:35.704580 175  Options.max_write_buffer_number: 2
2025/05/29-10:11:35.704582 175          Options.compression: LZ4
2025/05/29-10:11:35.704584 175                  Options.bottommost_compression: Disabled
2025/05/29-10:11:35.704586 175       Options.prefix_extractor: nullptr
2025/05/29-10:11:35.704587 175   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/29-10:11:35.704590 175             Options.num_levels: 7
2025/05/29-10:11:35.704591 175        Options.min_write_buffer_number_to_merge: 1
2025/05/29-10:11:35.704593 175     Options.max_write_buffer_number_to_maintain: 0
2025/05/29-10:11:35.704596 175     Options.max_write_buffer_size_to_maintain: 0
2025/05/29-10:11:35.704598 175            Options.bottommost_compression_opts.window_bits: -14
2025/05/29-10:11:35.704612 175                  Options.bottommost_compression_opts.level: 32767
2025/05/29-10:11:35.704614 175               Options.bottommost_compression_opts.strategy: 0
2025/05/29-10:11:35.704616 175         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.704618 175         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.704619 175         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/29-10:11:35.704623 175                  Options.bottommost_compression_opts.enabled: false
2025/05/29-10:11:35.704624 175         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.704626 175         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.704628 175            Options.compression_opts.window_bits: -14
2025/05/29-10:11:35.704630 175                  Options.compression_opts.level: 32767
2025/05/29-10:11:35.704631 175               Options.compression_opts.strategy: 0
2025/05/29-10:11:35.704633 175         Options.compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.704635 175         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.704636 175         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.704638 175         Options.compression_opts.parallel_threads: 1
2025/05/29-10:11:35.704639 175                  Options.compression_opts.enabled: false
2025/05/29-10:11:35.704641 175         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.704644 175      Options.level0_file_num_compaction_trigger: 4
2025/05/29-10:11:35.704646 175          Options.level0_slowdown_writes_trigger: 20
2025/05/29-10:11:35.704648 175              Options.level0_stop_writes_trigger: 36
2025/05/29-10:11:35.704651 175                   Options.target_file_size_base: 67108864
2025/05/29-10:11:35.704653 175             Options.target_file_size_multiplier: 1
2025/05/29-10:11:35.704654 175                Options.max_bytes_for_level_base: 268435456
2025/05/29-10:11:35.704656 175 Options.level_compaction_dynamic_level_bytes: 1
2025/05/29-10:11:35.704660 175          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/29-10:11:35.704662 175 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/29-10:11:35.704664 175 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/29-10:11:35.704666 175 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/29-10:11:35.704674 175 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/29-10:11:35.704676 175 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/29-10:11:35.704679 175 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/29-10:11:35.704681 175 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/29-10:11:35.704694 175       Options.max_sequential_skip_in_iterations: 8
2025/05/29-10:11:35.704697 175                    Options.max_compaction_bytes: 1677721600
2025/05/29-10:11:35.704699 175                        Options.arena_block_size: 1048576
2025/05/29-10:11:35.704700 175   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/29-10:11:35.704702 175   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/29-10:11:35.704704 175                Options.disable_auto_compactions: 0
2025/05/29-10:11:35.704706 175                        Options.compaction_style: kCompactionStyleLevel
2025/05/29-10:11:35.704709 175                          Options.compaction_pri: kMinOverlappingRatio
2025/05/29-10:11:35.704711 175 Options.compaction_options_universal.size_ratio: 1
2025/05/29-10:11:35.704713 175 Options.compaction_options_universal.min_merge_width: 2
2025/05/29-10:11:35.704715 175 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/29-10:11:35.704716 175 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/29-10:11:35.704720 175 Options.compaction_options_universal.compression_size_percent: -1
2025/05/29-10:11:35.704723 175 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/29-10:11:35.704725 175 Options.compaction_options_universal.max_read_amp: -1
2025/05/29-10:11:35.704727 175 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/29-10:11:35.704729 175 Options.compaction_options_fifo.allow_compaction: 0
2025/05/29-10:11:35.704733 175                   Options.table_properties_collectors: 
2025/05/29-10:11:35.704735 175                   Options.inplace_update_support: 0
2025/05/29-10:11:35.704738 175                 Options.inplace_update_num_locks: 10000
2025/05/29-10:11:35.704740 175               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/29-10:11:35.704745 175               Options.memtable_whole_key_filtering: 0
2025/05/29-10:11:35.704747 175   Options.memtable_huge_page_size: 0
2025/05/29-10:11:35.704766 175                           Options.bloom_locality: 0
2025/05/29-10:11:35.704769 175                    Options.max_successive_merges: 0
2025/05/29-10:11:35.704771 175             Options.strict_max_successive_merges: 0
2025/05/29-10:11:35.704773 175                Options.optimize_filters_for_hits: 0
2025/05/29-10:11:35.704775 175                Options.paranoid_file_checks: 0
2025/05/29-10:11:35.704776 175                Options.force_consistency_checks: 1
2025/05/29-10:11:35.704778 175                Options.report_bg_io_stats: 0
2025/05/29-10:11:35.704780 175                               Options.ttl: 2592000
2025/05/29-10:11:35.704783 175          Options.periodic_compaction_seconds: 0
2025/05/29-10:11:35.704786 175                        Options.default_temperature: kUnknown
2025/05/29-10:11:35.704796 175  Options.preclude_last_level_data_seconds: 0
2025/05/29-10:11:35.704798 175    Options.preserve_internal_time_seconds: 0
2025/05/29-10:11:35.704799 175                       Options.enable_blob_files: false
2025/05/29-10:11:35.704801 175                           Options.min_blob_size: 0
2025/05/29-10:11:35.704802 175                          Options.blob_file_size: 268435456
2025/05/29-10:11:35.704804 175                   Options.blob_compression_type: NoCompression
2025/05/29-10:11:35.704806 175          Options.enable_blob_garbage_collection: false
2025/05/29-10:11:35.704809 175      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/29-10:11:35.704811 175 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/29-10:11:35.704814 175          Options.blob_compaction_readahead_size: 0
2025/05/29-10:11:35.704816 175                Options.blob_file_starting_level: 0
2025/05/29-10:11:35.704817 175         Options.experimental_mempurge_threshold: 0.000000
2025/05/29-10:11:35.704819 175            Options.memtable_max_range_deletions: 0
2025/05/29-10:11:35.717221 175               Options.comparator: leveldb.BytewiseComparator
2025/05/29-10:11:35.717226 175           Options.merge_operator: None
2025/05/29-10:11:35.717229 175        Options.compaction_filter: None
2025/05/29-10:11:35.717231 175        Options.compaction_filter_factory: None
2025/05/29-10:11:35.717233 175  Options.sst_partitioner_factory: None
2025/05/29-10:11:35.717235 175         Options.memtable_factory: SkipListFactory
2025/05/29-10:11:35.717237 175            Options.table_factory: BlockBasedTable
2025/05/29-10:11:35.717263 175            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7be3f0c00ce0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7be3f0c36010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/29-10:11:35.717269 175        Options.write_buffer_size: 10485760
2025/05/29-10:11:35.717271 175  Options.max_write_buffer_number: 2
2025/05/29-10:11:35.717276 175          Options.compression: LZ4
2025/05/29-10:11:35.717278 175                  Options.bottommost_compression: Disabled
2025/05/29-10:11:35.717280 175       Options.prefix_extractor: nullptr
2025/05/29-10:11:35.717281 175   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/29-10:11:35.717290 175             Options.num_levels: 7
2025/05/29-10:11:35.717292 175        Options.min_write_buffer_number_to_merge: 1
2025/05/29-10:11:35.717296 175     Options.max_write_buffer_number_to_maintain: 0
2025/05/29-10:11:35.717298 175     Options.max_write_buffer_size_to_maintain: 0
2025/05/29-10:11:35.717304 175            Options.bottommost_compression_opts.window_bits: -14
2025/05/29-10:11:35.717307 175                  Options.bottommost_compression_opts.level: 32767
2025/05/29-10:11:35.717309 175               Options.bottommost_compression_opts.strategy: 0
2025/05/29-10:11:35.717311 175         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.717313 175         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.717315 175         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/29-10:11:35.717319 175                  Options.bottommost_compression_opts.enabled: false
2025/05/29-10:11:35.717321 175         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.717323 175         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.717326 175            Options.compression_opts.window_bits: -14
2025/05/29-10:11:35.717328 175                  Options.compression_opts.level: 32767
2025/05/29-10:11:35.717332 175               Options.compression_opts.strategy: 0
2025/05/29-10:11:35.717333 175         Options.compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.717335 175         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.717337 175         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.717339 175         Options.compression_opts.parallel_threads: 1
2025/05/29-10:11:35.717340 175                  Options.compression_opts.enabled: false
2025/05/29-10:11:35.717342 175         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.717344 175      Options.level0_file_num_compaction_trigger: 4
2025/05/29-10:11:35.717346 175          Options.level0_slowdown_writes_trigger: 20
2025/05/29-10:11:35.717348 175              Options.level0_stop_writes_trigger: 36
2025/05/29-10:11:35.717350 175                   Options.target_file_size_base: 67108864
2025/05/29-10:11:35.717351 175             Options.target_file_size_multiplier: 1
2025/05/29-10:11:35.717353 175                Options.max_bytes_for_level_base: 268435456
2025/05/29-10:11:35.717355 175 Options.level_compaction_dynamic_level_bytes: 1
2025/05/29-10:11:35.717360 175          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/29-10:11:35.717363 175 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/29-10:11:35.717365 175 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/29-10:11:35.717369 175 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/29-10:11:35.717371 175 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/29-10:11:35.717373 175 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/29-10:11:35.717379 175 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/29-10:11:35.717381 175 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/29-10:11:35.717383 175       Options.max_sequential_skip_in_iterations: 8
2025/05/29-10:11:35.717385 175                    Options.max_compaction_bytes: 1677721600
2025/05/29-10:11:35.717387 175                        Options.arena_block_size: 1048576
2025/05/29-10:11:35.717389 175   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/29-10:11:35.717391 175   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/29-10:11:35.717395 175                Options.disable_auto_compactions: 0
2025/05/29-10:11:35.717398 175                        Options.compaction_style: kCompactionStyleLevel
2025/05/29-10:11:35.717400 175                          Options.compaction_pri: kMinOverlappingRatio
2025/05/29-10:11:35.717402 175 Options.compaction_options_universal.size_ratio: 1
2025/05/29-10:11:35.717405 175 Options.compaction_options_universal.min_merge_width: 2
2025/05/29-10:11:35.717409 175 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/29-10:11:35.717411 175 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/29-10:11:35.717414 175 Options.compaction_options_universal.compression_size_percent: -1
2025/05/29-10:11:35.717416 175 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/29-10:11:35.717418 175 Options.compaction_options_universal.max_read_amp: -1
2025/05/29-10:11:35.717434 175 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/29-10:11:35.717437 175 Options.compaction_options_fifo.allow_compaction: 0
2025/05/29-10:11:35.717443 175                   Options.table_properties_collectors: 
2025/05/29-10:11:35.717445 175                   Options.inplace_update_support: 0
2025/05/29-10:11:35.717446 175                 Options.inplace_update_num_locks: 10000
2025/05/29-10:11:35.717448 175               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/29-10:11:35.717450 175               Options.memtable_whole_key_filtering: 0
2025/05/29-10:11:35.717452 175   Options.memtable_huge_page_size: 0
2025/05/29-10:11:35.717454 175                           Options.bloom_locality: 0
2025/05/29-10:11:35.717455 175                    Options.max_successive_merges: 0
2025/05/29-10:11:35.717457 175             Options.strict_max_successive_merges: 0
2025/05/29-10:11:35.717459 175                Options.optimize_filters_for_hits: 0
2025/05/29-10:11:35.717461 175                Options.paranoid_file_checks: 0
2025/05/29-10:11:35.717482 175                Options.force_consistency_checks: 1
2025/05/29-10:11:35.717510 175                Options.report_bg_io_stats: 0
2025/05/29-10:11:35.717512 175                               Options.ttl: 2592000
2025/05/29-10:11:35.717513 175          Options.periodic_compaction_seconds: 0
2025/05/29-10:11:35.717515 175                        Options.default_temperature: kUnknown
2025/05/29-10:11:35.717517 175  Options.preclude_last_level_data_seconds: 0
2025/05/29-10:11:35.717518 175    Options.preserve_internal_time_seconds: 0
2025/05/29-10:11:35.717520 175                       Options.enable_blob_files: false
2025/05/29-10:11:35.717521 175                           Options.min_blob_size: 0
2025/05/29-10:11:35.717522 175                          Options.blob_file_size: 268435456
2025/05/29-10:11:35.717524 175                   Options.blob_compression_type: NoCompression
2025/05/29-10:11:35.717525 175          Options.enable_blob_garbage_collection: false
2025/05/29-10:11:35.717527 175      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/29-10:11:35.717529 175 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/29-10:11:35.717530 175          Options.blob_compaction_readahead_size: 0
2025/05/29-10:11:35.717532 175                Options.blob_file_starting_level: 0
2025/05/29-10:11:35.717533 175         Options.experimental_mempurge_threshold: 0.000000
2025/05/29-10:11:35.717535 175            Options.memtable_max_range_deletions: 0
2025/05/29-10:11:35.726858 175 DB pointer 0x7be3f0c72c00
