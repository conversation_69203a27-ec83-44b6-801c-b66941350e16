2025/05/29-10:11:35.651358 179 RocksDB version: 9.9.3
2025/05/29-10:11:35.651489 179 Compile date 2024-12-05 01:25:31
2025/05/29-10:11:35.651496 179 DB SUMMARY
2025/05/29-10:11:35.651499 179 Host name (Env):  beb888368ddf
2025/05/29-10:11:35.651501 179 DB Session ID:  Z3WRR5PFQNLDVUSNLKZS
2025/05/29-10:11:35.651544 179 SST files in ./storage/collections/kaizen_documents/0/segments/d098618b-c02b-4b62-b95d-de8b1018d1ee dir, Total Num: 0, files: 
2025/05/29-10:11:35.651547 179 Write Ahead Log file in ./storage/collections/kaizen_documents/0/segments/d098618b-c02b-4b62-b95d-de8b1018d1ee: 
2025/05/29-10:11:35.651552 179                         Options.error_if_exists: 0
2025/05/29-10:11:35.651555 179                       Options.create_if_missing: 1
2025/05/29-10:11:35.651557 179                         Options.paranoid_checks: 1
2025/05/29-10:11:35.651560 179             Options.flush_verify_memtable_count: 1
2025/05/29-10:11:35.651562 179          Options.compaction_verify_record_count: 1
2025/05/29-10:11:35.651564 179                               Options.track_and_verify_wals_in_manifest: 0
2025/05/29-10:11:35.651567 179        Options.verify_sst_unique_id_in_manifest: 1
2025/05/29-10:11:35.651568 179                                     Options.env: 0x7be3ef622000
2025/05/29-10:11:35.651571 179                                      Options.fs: PosixFileSystem
2025/05/29-10:11:35.651574 179                                Options.info_log: 0x7be3eee86000
2025/05/29-10:11:35.651576 179                Options.max_file_opening_threads: 16
2025/05/29-10:11:35.651579 179                              Options.statistics: (nil)
2025/05/29-10:11:35.651581 179                               Options.use_fsync: 0
2025/05/29-10:11:35.651585 179                       Options.max_log_file_size: 1048576
2025/05/29-10:11:35.651586 179                  Options.max_manifest_file_size: 1073741824
2025/05/29-10:11:35.651589 179                   Options.log_file_time_to_roll: 0
2025/05/29-10:11:35.651606 179                       Options.keep_log_file_num: 1
2025/05/29-10:11:35.651607 179                    Options.recycle_log_file_num: 0
2025/05/29-10:11:35.651610 179                         Options.allow_fallocate: 1
2025/05/29-10:11:35.651615 179                        Options.allow_mmap_reads: 0
2025/05/29-10:11:35.651618 179                       Options.allow_mmap_writes: 0
2025/05/29-10:11:35.651623 179                        Options.use_direct_reads: 0
2025/05/29-10:11:35.651624 179                        Options.use_direct_io_for_flush_and_compaction: 0
2025/05/29-10:11:35.651628 179          Options.create_missing_column_families: 1
2025/05/29-10:11:35.651633 179                              Options.db_log_dir: 
2025/05/29-10:11:35.651652 179                                 Options.wal_dir: 
2025/05/29-10:11:35.651653 179                Options.table_cache_numshardbits: 6
2025/05/29-10:11:35.651655 179                         Options.WAL_ttl_seconds: 0
2025/05/29-10:11:35.651658 179                       Options.WAL_size_limit_MB: 0
2025/05/29-10:11:35.651660 179                        Options.max_write_batch_group_size_bytes: 1048576
2025/05/29-10:11:35.651672 179             Options.manifest_preallocation_size: 4194304
2025/05/29-10:11:35.651680 179                     Options.is_fd_close_on_exec: 1
2025/05/29-10:11:35.651681 179                   Options.advise_random_on_open: 1
2025/05/29-10:11:35.651684 179                    Options.db_write_buffer_size: 0
2025/05/29-10:11:35.651685 179                    Options.write_buffer_manager: 0x7be3eee04e80
2025/05/29-10:11:35.651687 179           Options.random_access_max_buffer_size: 1048576
2025/05/29-10:11:35.651689 179                      Options.use_adaptive_mutex: 0
2025/05/29-10:11:35.651692 179                            Options.rate_limiter: (nil)
2025/05/29-10:11:35.651696 179     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/05/29-10:11:35.651702 179                       Options.wal_recovery_mode: 0
2025/05/29-10:11:35.651704 179                  Options.enable_thread_tracking: 0
2025/05/29-10:11:35.651715 179                  Options.enable_pipelined_write: 0
2025/05/29-10:11:35.651719 179                  Options.unordered_write: 0
2025/05/29-10:11:35.651720 179         Options.allow_concurrent_memtable_write: 1
2025/05/29-10:11:35.651723 179      Options.enable_write_thread_adaptive_yield: 1
2025/05/29-10:11:35.651726 179             Options.write_thread_max_yield_usec: 100
2025/05/29-10:11:35.651730 179            Options.write_thread_slow_yield_usec: 3
2025/05/29-10:11:35.651742 179                               Options.row_cache: None
2025/05/29-10:11:35.651743 179                              Options.wal_filter: None
2025/05/29-10:11:35.651745 179             Options.avoid_flush_during_recovery: 0
2025/05/29-10:11:35.651747 179             Options.allow_ingest_behind: 0
2025/05/29-10:11:35.651750 179             Options.two_write_queues: 0
2025/05/29-10:11:35.651757 179             Options.manual_wal_flush: 0
2025/05/29-10:11:35.651765 179             Options.wal_compression: 0
2025/05/29-10:11:35.651773 179             Options.background_close_inactive_wals: 0
2025/05/29-10:11:35.651775 179             Options.atomic_flush: 0
2025/05/29-10:11:35.651776 179             Options.avoid_unnecessary_blocking_io: 0
2025/05/29-10:11:35.651788 179             Options.prefix_seek_opt_in_only: 0
2025/05/29-10:11:35.651790 179                 Options.persist_stats_to_disk: 0
2025/05/29-10:11:35.651792 179                 Options.write_dbid_to_manifest: 1
2025/05/29-10:11:35.651798 179                 Options.write_identity_file: 1
2025/05/29-10:11:35.651800 179                 Options.log_readahead_size: 0
2025/05/29-10:11:35.651802 179                 Options.file_checksum_gen_factory: Unknown
2025/05/29-10:11:35.651804 179                 Options.best_efforts_recovery: 0
2025/05/29-10:11:35.651808 179                Options.max_bgerror_resume_count: 2147483647
2025/05/29-10:11:35.651810 179            Options.bgerror_resume_retry_interval: 1000000
2025/05/29-10:11:35.651813 179             Options.allow_data_in_errors: 0
2025/05/29-10:11:35.651814 179             Options.db_host_id: __hostname__
2025/05/29-10:11:35.651829 179             Options.enforce_single_del_contracts: true
2025/05/29-10:11:35.651860 179             Options.metadata_write_temperature: kUnknown
2025/05/29-10:11:35.651861 179             Options.wal_write_temperature: kUnknown
2025/05/29-10:11:35.651866 179             Options.max_background_jobs: 2
2025/05/29-10:11:35.651868 179             Options.max_background_compactions: -1
2025/05/29-10:11:35.651870 179             Options.max_subcompactions: 1
2025/05/29-10:11:35.651874 179             Options.avoid_flush_during_shutdown: 0
2025/05/29-10:11:35.651877 179           Options.writable_file_max_buffer_size: 1048576
2025/05/29-10:11:35.651894 179             Options.delayed_write_rate : 16777216
2025/05/29-10:11:35.651914 179             Options.max_total_wal_size: 0
2025/05/29-10:11:35.651920 179             Options.delete_obsolete_files_period_micros: 180000000
2025/05/29-10:11:35.651924 179                   Options.stats_dump_period_sec: 600
2025/05/29-10:11:35.651933 179                 Options.stats_persist_period_sec: 600
2025/05/29-10:11:35.651935 179                 Options.stats_history_buffer_size: 1048576
2025/05/29-10:11:35.651936 179                          Options.max_open_files: 256
2025/05/29-10:11:35.651938 179                          Options.bytes_per_sync: 0
2025/05/29-10:11:35.651939 179                      Options.wal_bytes_per_sync: 0
2025/05/29-10:11:35.651946 179                   Options.strict_bytes_per_sync: 0
2025/05/29-10:11:35.651965 179       Options.compaction_readahead_size: 2097152
2025/05/29-10:11:35.651966 179                  Options.max_background_flushes: -1
2025/05/29-10:11:35.651977 179 Options.daily_offpeak_time_utc: 
2025/05/29-10:11:35.651979 179 Compression algorithms supported:
2025/05/29-10:11:35.651981 179 	kZSTD supported: 0
2025/05/29-10:11:35.651984 179 	kXpressCompression supported: 0
2025/05/29-10:11:35.652010 179 	kBZip2Compression supported: 0
2025/05/29-10:11:35.652011 179 	kZSTDNotFinalCompression supported: 0
2025/05/29-10:11:35.652018 179 	kLZ4Compression supported: 1
2025/05/29-10:11:35.652025 179 	kZlibCompression supported: 0
2025/05/29-10:11:35.652040 179 	kLZ4HCCompression supported: 1
2025/05/29-10:11:35.652046 179 	kSnappyCompression supported: 1
2025/05/29-10:11:35.652051 179 Fast CRC32 supported: Not supported on x86
2025/05/29-10:11:35.652053 179 DMutex implementation: pthread_mutex_t
2025/05/29-10:11:35.652054 179 Jemalloc supported: 0
2025/05/29-10:11:35.676262 179               Options.comparator: leveldb.BytewiseComparator
2025/05/29-10:11:35.676267 179           Options.merge_operator: None
2025/05/29-10:11:35.676270 179        Options.compaction_filter: None
2025/05/29-10:11:35.676272 179        Options.compaction_filter_factory: None
2025/05/29-10:11:35.676274 179  Options.sst_partitioner_factory: None
2025/05/29-10:11:35.676276 179         Options.memtable_factory: SkipListFactory
2025/05/29-10:11:35.676280 179            Options.table_factory: BlockBasedTable
2025/05/29-10:11:35.676342 179            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7be3eee00ce0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7be3eee2a010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/29-10:11:35.676353 179        Options.write_buffer_size: 10485760
2025/05/29-10:11:35.676357 179  Options.max_write_buffer_number: 2
2025/05/29-10:11:35.676362 179          Options.compression: LZ4
2025/05/29-10:11:35.676364 179                  Options.bottommost_compression: Disabled
2025/05/29-10:11:35.676366 179       Options.prefix_extractor: nullptr
2025/05/29-10:11:35.676381 179   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/29-10:11:35.676383 179             Options.num_levels: 7
2025/05/29-10:11:35.676385 179        Options.min_write_buffer_number_to_merge: 1
2025/05/29-10:11:35.676387 179     Options.max_write_buffer_number_to_maintain: 0
2025/05/29-10:11:35.676389 179     Options.max_write_buffer_size_to_maintain: 0
2025/05/29-10:11:35.676391 179            Options.bottommost_compression_opts.window_bits: -14
2025/05/29-10:11:35.676411 179                  Options.bottommost_compression_opts.level: 32767
2025/05/29-10:11:35.676414 179               Options.bottommost_compression_opts.strategy: 0
2025/05/29-10:11:35.676416 179         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.676419 179         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.676421 179         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/29-10:11:35.676423 179                  Options.bottommost_compression_opts.enabled: false
2025/05/29-10:11:35.676425 179         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.676429 179         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.676433 179            Options.compression_opts.window_bits: -14
2025/05/29-10:11:35.676435 179                  Options.compression_opts.level: 32767
2025/05/29-10:11:35.676438 179               Options.compression_opts.strategy: 0
2025/05/29-10:11:35.676449 179         Options.compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.676451 179         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.676453 179         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.676455 179         Options.compression_opts.parallel_threads: 1
2025/05/29-10:11:35.676457 179                  Options.compression_opts.enabled: false
2025/05/29-10:11:35.676462 179         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.676464 179      Options.level0_file_num_compaction_trigger: 4
2025/05/29-10:11:35.676466 179          Options.level0_slowdown_writes_trigger: 20
2025/05/29-10:11:35.676468 179              Options.level0_stop_writes_trigger: 36
2025/05/29-10:11:35.676470 179                   Options.target_file_size_base: 67108864
2025/05/29-10:11:35.676473 179             Options.target_file_size_multiplier: 1
2025/05/29-10:11:35.676475 179                Options.max_bytes_for_level_base: 268435456
2025/05/29-10:11:35.676479 179 Options.level_compaction_dynamic_level_bytes: 1
2025/05/29-10:11:35.676482 179          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/29-10:11:35.676486 179 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/29-10:11:35.676488 179 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/29-10:11:35.676491 179 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/29-10:11:35.676493 179 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/29-10:11:35.676495 179 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/29-10:11:35.676497 179 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/29-10:11:35.676499 179 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/29-10:11:35.676505 179       Options.max_sequential_skip_in_iterations: 8
2025/05/29-10:11:35.676508 179                    Options.max_compaction_bytes: 1677721600
2025/05/29-10:11:35.676511 179                        Options.arena_block_size: 1048576
2025/05/29-10:11:35.676513 179   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/29-10:11:35.676515 179   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/29-10:11:35.676517 179                Options.disable_auto_compactions: 0
2025/05/29-10:11:35.676522 179                        Options.compaction_style: kCompactionStyleLevel
2025/05/29-10:11:35.676596 179                          Options.compaction_pri: kMinOverlappingRatio
2025/05/29-10:11:35.676603 179 Options.compaction_options_universal.size_ratio: 1
2025/05/29-10:11:35.676605 179 Options.compaction_options_universal.min_merge_width: 2
2025/05/29-10:11:35.676607 179 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/29-10:11:35.676609 179 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/29-10:11:35.676611 179 Options.compaction_options_universal.compression_size_percent: -1
2025/05/29-10:11:35.676613 179 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/29-10:11:35.676623 179 Options.compaction_options_universal.max_read_amp: -1
2025/05/29-10:11:35.676625 179 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/29-10:11:35.676627 179 Options.compaction_options_fifo.allow_compaction: 0
2025/05/29-10:11:35.676634 179                   Options.table_properties_collectors: 
2025/05/29-10:11:35.676636 179                   Options.inplace_update_support: 0
2025/05/29-10:11:35.676638 179                 Options.inplace_update_num_locks: 10000
2025/05/29-10:11:35.676641 179               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/29-10:11:35.676643 179               Options.memtable_whole_key_filtering: 0
2025/05/29-10:11:35.676646 179   Options.memtable_huge_page_size: 0
2025/05/29-10:11:35.676667 179                           Options.bloom_locality: 0
2025/05/29-10:11:35.676669 179                    Options.max_successive_merges: 0
2025/05/29-10:11:35.676671 179             Options.strict_max_successive_merges: 0
2025/05/29-10:11:35.676679 179                Options.optimize_filters_for_hits: 0
2025/05/29-10:11:35.676681 179                Options.paranoid_file_checks: 0
2025/05/29-10:11:35.676684 179                Options.force_consistency_checks: 1
2025/05/29-10:11:35.676686 179                Options.report_bg_io_stats: 0
2025/05/29-10:11:35.676688 179                               Options.ttl: 2592000
2025/05/29-10:11:35.676690 179          Options.periodic_compaction_seconds: 0
2025/05/29-10:11:35.676719 179                        Options.default_temperature: kUnknown
2025/05/29-10:11:35.676723 179  Options.preclude_last_level_data_seconds: 0
2025/05/29-10:11:35.676725 179    Options.preserve_internal_time_seconds: 0
2025/05/29-10:11:35.676728 179                       Options.enable_blob_files: false
2025/05/29-10:11:35.676730 179                           Options.min_blob_size: 0
2025/05/29-10:11:35.676732 179                          Options.blob_file_size: 268435456
2025/05/29-10:11:35.676735 179                   Options.blob_compression_type: NoCompression
2025/05/29-10:11:35.676737 179          Options.enable_blob_garbage_collection: false
2025/05/29-10:11:35.676739 179      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/29-10:11:35.676743 179 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/29-10:11:35.676745 179          Options.blob_compaction_readahead_size: 0
2025/05/29-10:11:35.676747 179                Options.blob_file_starting_level: 0
2025/05/29-10:11:35.676749 179         Options.experimental_mempurge_threshold: 0.000000
2025/05/29-10:11:35.676752 179            Options.memtable_max_range_deletions: 0
2025/05/29-10:11:35.699633 179               Options.comparator: leveldb.BytewiseComparator
2025/05/29-10:11:35.699638 179           Options.merge_operator: None
2025/05/29-10:11:35.699640 179        Options.compaction_filter: None
2025/05/29-10:11:35.699660 179        Options.compaction_filter_factory: None
2025/05/29-10:11:35.699662 179  Options.sst_partitioner_factory: None
2025/05/29-10:11:35.699663 179         Options.memtable_factory: SkipListFactory
2025/05/29-10:11:35.699665 179            Options.table_factory: BlockBasedTable
2025/05/29-10:11:35.699690 179            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7be3eee00ce0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7be3eee2a010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/29-10:11:35.699722 179        Options.write_buffer_size: 10485760
2025/05/29-10:11:35.699724 179  Options.max_write_buffer_number: 2
2025/05/29-10:11:35.699726 179          Options.compression: LZ4
2025/05/29-10:11:35.699727 179                  Options.bottommost_compression: Disabled
2025/05/29-10:11:35.699729 179       Options.prefix_extractor: nullptr
2025/05/29-10:11:35.699730 179   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/29-10:11:35.699732 179             Options.num_levels: 7
2025/05/29-10:11:35.699733 179        Options.min_write_buffer_number_to_merge: 1
2025/05/29-10:11:35.699735 179     Options.max_write_buffer_number_to_maintain: 0
2025/05/29-10:11:35.699736 179     Options.max_write_buffer_size_to_maintain: 0
2025/05/29-10:11:35.699738 179            Options.bottommost_compression_opts.window_bits: -14
2025/05/29-10:11:35.699740 179                  Options.bottommost_compression_opts.level: 32767
2025/05/29-10:11:35.699742 179               Options.bottommost_compression_opts.strategy: 0
2025/05/29-10:11:35.699744 179         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.699745 179         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.699747 179         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/29-10:11:35.699748 179                  Options.bottommost_compression_opts.enabled: false
2025/05/29-10:11:35.699750 179         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.699752 179         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.699753 179            Options.compression_opts.window_bits: -14
2025/05/29-10:11:35.699755 179                  Options.compression_opts.level: 32767
2025/05/29-10:11:35.699757 179               Options.compression_opts.strategy: 0
2025/05/29-10:11:35.699758 179         Options.compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.699760 179         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.699761 179         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.699765 179         Options.compression_opts.parallel_threads: 1
2025/05/29-10:11:35.699768 179                  Options.compression_opts.enabled: false
2025/05/29-10:11:35.699770 179         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.699773 179      Options.level0_file_num_compaction_trigger: 4
2025/05/29-10:11:35.699776 179          Options.level0_slowdown_writes_trigger: 20
2025/05/29-10:11:35.699793 179              Options.level0_stop_writes_trigger: 36
2025/05/29-10:11:35.699796 179                   Options.target_file_size_base: 67108864
2025/05/29-10:11:35.699798 179             Options.target_file_size_multiplier: 1
2025/05/29-10:11:35.699800 179                Options.max_bytes_for_level_base: 268435456
2025/05/29-10:11:35.699801 179 Options.level_compaction_dynamic_level_bytes: 1
2025/05/29-10:11:35.699804 179          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/29-10:11:35.699806 179 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/29-10:11:35.699808 179 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/29-10:11:35.699810 179 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/29-10:11:35.699814 179 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/29-10:11:35.699816 179 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/29-10:11:35.699818 179 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/29-10:11:35.699819 179 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/29-10:11:35.699821 179       Options.max_sequential_skip_in_iterations: 8
2025/05/29-10:11:35.699823 179                    Options.max_compaction_bytes: 1677721600
2025/05/29-10:11:35.699825 179                        Options.arena_block_size: 1048576
2025/05/29-10:11:35.699827 179   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/29-10:11:35.699828 179   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/29-10:11:35.699830 179                Options.disable_auto_compactions: 0
2025/05/29-10:11:35.699833 179                        Options.compaction_style: kCompactionStyleLevel
2025/05/29-10:11:35.699836 179                          Options.compaction_pri: kMinOverlappingRatio
2025/05/29-10:11:35.699838 179 Options.compaction_options_universal.size_ratio: 1
2025/05/29-10:11:35.699841 179 Options.compaction_options_universal.min_merge_width: 2
2025/05/29-10:11:35.699846 179 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/29-10:11:35.699848 179 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/29-10:11:35.699851 179 Options.compaction_options_universal.compression_size_percent: -1
2025/05/29-10:11:35.699853 179 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/29-10:11:35.699881 179 Options.compaction_options_universal.max_read_amp: -1
2025/05/29-10:11:35.699892 179 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/29-10:11:35.699894 179 Options.compaction_options_fifo.allow_compaction: 0
2025/05/29-10:11:35.699899 179                   Options.table_properties_collectors: 
2025/05/29-10:11:35.699900 179                   Options.inplace_update_support: 0
2025/05/29-10:11:35.699902 179                 Options.inplace_update_num_locks: 10000
2025/05/29-10:11:35.699904 179               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/29-10:11:35.699907 179               Options.memtable_whole_key_filtering: 0
2025/05/29-10:11:35.699908 179   Options.memtable_huge_page_size: 0
2025/05/29-10:11:35.699910 179                           Options.bloom_locality: 0
2025/05/29-10:11:35.699912 179                    Options.max_successive_merges: 0
2025/05/29-10:11:35.699913 179             Options.strict_max_successive_merges: 0
2025/05/29-10:11:35.699915 179                Options.optimize_filters_for_hits: 0
2025/05/29-10:11:35.699917 179                Options.paranoid_file_checks: 0
2025/05/29-10:11:35.699920 179                Options.force_consistency_checks: 1
2025/05/29-10:11:35.699921 179                Options.report_bg_io_stats: 0
2025/05/29-10:11:35.699933 179                               Options.ttl: 2592000
2025/05/29-10:11:35.699936 179          Options.periodic_compaction_seconds: 0
2025/05/29-10:11:35.699939 179                        Options.default_temperature: kUnknown
2025/05/29-10:11:35.699944 179  Options.preclude_last_level_data_seconds: 0
2025/05/29-10:11:35.699946 179    Options.preserve_internal_time_seconds: 0
2025/05/29-10:11:35.699947 179                       Options.enable_blob_files: false
2025/05/29-10:11:35.699949 179                           Options.min_blob_size: 0
2025/05/29-10:11:35.699951 179                          Options.blob_file_size: 268435456
2025/05/29-10:11:35.699953 179                   Options.blob_compression_type: NoCompression
2025/05/29-10:11:35.699956 179          Options.enable_blob_garbage_collection: false
2025/05/29-10:11:35.699958 179      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/29-10:11:35.699961 179 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/29-10:11:35.699968 179          Options.blob_compaction_readahead_size: 0
2025/05/29-10:11:35.699970 179                Options.blob_file_starting_level: 0
2025/05/29-10:11:35.699972 179         Options.experimental_mempurge_threshold: 0.000000
2025/05/29-10:11:35.699974 179            Options.memtable_max_range_deletions: 0
2025/05/29-10:11:35.704499 179               Options.comparator: leveldb.BytewiseComparator
2025/05/29-10:11:35.704504 179           Options.merge_operator: None
2025/05/29-10:11:35.704508 179        Options.compaction_filter: None
2025/05/29-10:11:35.704510 179        Options.compaction_filter_factory: None
2025/05/29-10:11:35.704511 179  Options.sst_partitioner_factory: None
2025/05/29-10:11:35.704513 179         Options.memtable_factory: SkipListFactory
2025/05/29-10:11:35.704521 179            Options.table_factory: BlockBasedTable
2025/05/29-10:11:35.704546 179            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7be3eee00ce0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7be3eee2a010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/29-10:11:35.704553 179        Options.write_buffer_size: 10485760
2025/05/29-10:11:35.704555 179  Options.max_write_buffer_number: 2
2025/05/29-10:11:35.704557 179          Options.compression: LZ4
2025/05/29-10:11:35.704558 179                  Options.bottommost_compression: Disabled
2025/05/29-10:11:35.704560 179       Options.prefix_extractor: nullptr
2025/05/29-10:11:35.704562 179   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/29-10:11:35.704564 179             Options.num_levels: 7
2025/05/29-10:11:35.704565 179        Options.min_write_buffer_number_to_merge: 1
2025/05/29-10:11:35.704567 179     Options.max_write_buffer_number_to_maintain: 0
2025/05/29-10:11:35.704569 179     Options.max_write_buffer_size_to_maintain: 0
2025/05/29-10:11:35.704570 179            Options.bottommost_compression_opts.window_bits: -14
2025/05/29-10:11:35.704573 179                  Options.bottommost_compression_opts.level: 32767
2025/05/29-10:11:35.704577 179               Options.bottommost_compression_opts.strategy: 0
2025/05/29-10:11:35.704578 179         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.704580 179         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.704582 179         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/29-10:11:35.704584 179                  Options.bottommost_compression_opts.enabled: false
2025/05/29-10:11:35.704585 179         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.704587 179         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.704589 179            Options.compression_opts.window_bits: -14
2025/05/29-10:11:35.704592 179                  Options.compression_opts.level: 32767
2025/05/29-10:11:35.704594 179               Options.compression_opts.strategy: 0
2025/05/29-10:11:35.704597 179         Options.compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.704598 179         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.704600 179         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.704602 179         Options.compression_opts.parallel_threads: 1
2025/05/29-10:11:35.704604 179                  Options.compression_opts.enabled: false
2025/05/29-10:11:35.704605 179         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.704607 179      Options.level0_file_num_compaction_trigger: 4
2025/05/29-10:11:35.704609 179          Options.level0_slowdown_writes_trigger: 20
2025/05/29-10:11:35.704611 179              Options.level0_stop_writes_trigger: 36
2025/05/29-10:11:35.704612 179                   Options.target_file_size_base: 67108864
2025/05/29-10:11:35.704614 179             Options.target_file_size_multiplier: 1
2025/05/29-10:11:35.704616 179                Options.max_bytes_for_level_base: 268435456
2025/05/29-10:11:35.704618 179 Options.level_compaction_dynamic_level_bytes: 1
2025/05/29-10:11:35.704621 179          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/29-10:11:35.704623 179 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/29-10:11:35.704625 179 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/29-10:11:35.704626 179 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/29-10:11:35.704628 179 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/29-10:11:35.704630 179 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/29-10:11:35.704632 179 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/29-10:11:35.704647 179 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/29-10:11:35.704667 179       Options.max_sequential_skip_in_iterations: 8
2025/05/29-10:11:35.704669 179                    Options.max_compaction_bytes: 1677721600
2025/05/29-10:11:35.704671 179                        Options.arena_block_size: 1048576
2025/05/29-10:11:35.704674 179   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/29-10:11:35.704676 179   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/29-10:11:35.704678 179                Options.disable_auto_compactions: 0
2025/05/29-10:11:35.704681 179                        Options.compaction_style: kCompactionStyleLevel
2025/05/29-10:11:35.704688 179                          Options.compaction_pri: kMinOverlappingRatio
2025/05/29-10:11:35.704690 179 Options.compaction_options_universal.size_ratio: 1
2025/05/29-10:11:35.704693 179 Options.compaction_options_universal.min_merge_width: 2
2025/05/29-10:11:35.704695 179 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/29-10:11:35.704698 179 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/29-10:11:35.704700 179 Options.compaction_options_universal.compression_size_percent: -1
2025/05/29-10:11:35.704703 179 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/29-10:11:35.704705 179 Options.compaction_options_universal.max_read_amp: -1
2025/05/29-10:11:35.704707 179 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/29-10:11:35.704710 179 Options.compaction_options_fifo.allow_compaction: 0
2025/05/29-10:11:35.704714 179                   Options.table_properties_collectors: 
2025/05/29-10:11:35.704716 179                   Options.inplace_update_support: 0
2025/05/29-10:11:35.704718 179                 Options.inplace_update_num_locks: 10000
2025/05/29-10:11:35.704720 179               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/29-10:11:35.704722 179               Options.memtable_whole_key_filtering: 0
2025/05/29-10:11:35.704724 179   Options.memtable_huge_page_size: 0
2025/05/29-10:11:35.704726 179                           Options.bloom_locality: 0
2025/05/29-10:11:35.704728 179                    Options.max_successive_merges: 0
2025/05/29-10:11:35.704730 179             Options.strict_max_successive_merges: 0
2025/05/29-10:11:35.704731 179                Options.optimize_filters_for_hits: 0
2025/05/29-10:11:35.704737 179                Options.paranoid_file_checks: 0
2025/05/29-10:11:35.704739 179                Options.force_consistency_checks: 1
2025/05/29-10:11:35.704741 179                Options.report_bg_io_stats: 0
2025/05/29-10:11:35.704744 179                               Options.ttl: 2592000
2025/05/29-10:11:35.704746 179          Options.periodic_compaction_seconds: 0
2025/05/29-10:11:35.704748 179                        Options.default_temperature: kUnknown
2025/05/29-10:11:35.704750 179  Options.preclude_last_level_data_seconds: 0
2025/05/29-10:11:35.704763 179    Options.preserve_internal_time_seconds: 0
2025/05/29-10:11:35.704764 179                       Options.enable_blob_files: false
2025/05/29-10:11:35.704766 179                           Options.min_blob_size: 0
2025/05/29-10:11:35.704769 179                          Options.blob_file_size: 268435456
2025/05/29-10:11:35.704770 179                   Options.blob_compression_type: NoCompression
2025/05/29-10:11:35.704772 179          Options.enable_blob_garbage_collection: false
2025/05/29-10:11:35.704775 179      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/29-10:11:35.704777 179 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/29-10:11:35.704779 179          Options.blob_compaction_readahead_size: 0
2025/05/29-10:11:35.704780 179                Options.blob_file_starting_level: 0
2025/05/29-10:11:35.704783 179         Options.experimental_mempurge_threshold: 0.000000
2025/05/29-10:11:35.704798 179            Options.memtable_max_range_deletions: 0
2025/05/29-10:11:35.716924 179               Options.comparator: leveldb.BytewiseComparator
2025/05/29-10:11:35.716929 179           Options.merge_operator: None
2025/05/29-10:11:35.716930 179        Options.compaction_filter: None
2025/05/29-10:11:35.716932 179        Options.compaction_filter_factory: None
2025/05/29-10:11:35.716934 179  Options.sst_partitioner_factory: None
2025/05/29-10:11:35.716935 179         Options.memtable_factory: SkipListFactory
2025/05/29-10:11:35.716937 179            Options.table_factory: BlockBasedTable
2025/05/29-10:11:35.716963 179            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7be3eee00ce0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7be3eee2a010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/29-10:11:35.716966 179        Options.write_buffer_size: 10485760
2025/05/29-10:11:35.716968 179  Options.max_write_buffer_number: 2
2025/05/29-10:11:35.716970 179          Options.compression: LZ4
2025/05/29-10:11:35.716972 179                  Options.bottommost_compression: Disabled
2025/05/29-10:11:35.716973 179       Options.prefix_extractor: nullptr
2025/05/29-10:11:35.716975 179   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/29-10:11:35.716976 179             Options.num_levels: 7
2025/05/29-10:11:35.716978 179        Options.min_write_buffer_number_to_merge: 1
2025/05/29-10:11:35.716979 179     Options.max_write_buffer_number_to_maintain: 0
2025/05/29-10:11:35.716980 179     Options.max_write_buffer_size_to_maintain: 0
2025/05/29-10:11:35.716982 179            Options.bottommost_compression_opts.window_bits: -14
2025/05/29-10:11:35.716984 179                  Options.bottommost_compression_opts.level: 32767
2025/05/29-10:11:35.716986 179               Options.bottommost_compression_opts.strategy: 0
2025/05/29-10:11:35.716987 179         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.716989 179         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.716990 179         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/29-10:11:35.716992 179                  Options.bottommost_compression_opts.enabled: false
2025/05/29-10:11:35.716993 179         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.716995 179         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.716996 179            Options.compression_opts.window_bits: -14
2025/05/29-10:11:35.716998 179                  Options.compression_opts.level: 32767
2025/05/29-10:11:35.716999 179               Options.compression_opts.strategy: 0
2025/05/29-10:11:35.717001 179         Options.compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.717002 179         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.717004 179         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.717006 179         Options.compression_opts.parallel_threads: 1
2025/05/29-10:11:35.717007 179                  Options.compression_opts.enabled: false
2025/05/29-10:11:35.717009 179         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.717010 179      Options.level0_file_num_compaction_trigger: 4
2025/05/29-10:11:35.717013 179          Options.level0_slowdown_writes_trigger: 20
2025/05/29-10:11:35.717014 179              Options.level0_stop_writes_trigger: 36
2025/05/29-10:11:35.717016 179                   Options.target_file_size_base: 67108864
2025/05/29-10:11:35.717018 179             Options.target_file_size_multiplier: 1
2025/05/29-10:11:35.717020 179                Options.max_bytes_for_level_base: 268435456
2025/05/29-10:11:35.717022 179 Options.level_compaction_dynamic_level_bytes: 1
2025/05/29-10:11:35.717026 179          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/29-10:11:35.717029 179 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/29-10:11:35.717031 179 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/29-10:11:35.717034 179 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/29-10:11:35.717036 179 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/29-10:11:35.717041 179 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/29-10:11:35.717044 179 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/29-10:11:35.717046 179 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/29-10:11:35.717049 179       Options.max_sequential_skip_in_iterations: 8
2025/05/29-10:11:35.717051 179                    Options.max_compaction_bytes: 1677721600
2025/05/29-10:11:35.717053 179                        Options.arena_block_size: 1048576
2025/05/29-10:11:35.717056 179   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/29-10:11:35.717075 179   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/29-10:11:35.717078 179                Options.disable_auto_compactions: 0
2025/05/29-10:11:35.717081 179                        Options.compaction_style: kCompactionStyleLevel
2025/05/29-10:11:35.717084 179                          Options.compaction_pri: kMinOverlappingRatio
2025/05/29-10:11:35.717087 179 Options.compaction_options_universal.size_ratio: 1
2025/05/29-10:11:35.717101 179 Options.compaction_options_universal.min_merge_width: 2
2025/05/29-10:11:35.717104 179 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/29-10:11:35.717107 179 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/29-10:11:35.717109 179 Options.compaction_options_universal.compression_size_percent: -1
2025/05/29-10:11:35.717112 179 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/29-10:11:35.717115 179 Options.compaction_options_universal.max_read_amp: -1
2025/05/29-10:11:35.717118 179 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/29-10:11:35.717120 179 Options.compaction_options_fifo.allow_compaction: 0
2025/05/29-10:11:35.717126 179                   Options.table_properties_collectors: 
2025/05/29-10:11:35.717140 179                   Options.inplace_update_support: 0
2025/05/29-10:11:35.717142 179                 Options.inplace_update_num_locks: 10000
2025/05/29-10:11:35.717145 179               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/29-10:11:35.717148 179               Options.memtable_whole_key_filtering: 0
2025/05/29-10:11:35.717150 179   Options.memtable_huge_page_size: 0
2025/05/29-10:11:35.717152 179                           Options.bloom_locality: 0
2025/05/29-10:11:35.717166 179                    Options.max_successive_merges: 0
2025/05/29-10:11:35.717169 179             Options.strict_max_successive_merges: 0
2025/05/29-10:11:35.717171 179                Options.optimize_filters_for_hits: 0
2025/05/29-10:11:35.717174 179                Options.paranoid_file_checks: 0
2025/05/29-10:11:35.717176 179                Options.force_consistency_checks: 1
2025/05/29-10:11:35.717179 179                Options.report_bg_io_stats: 0
2025/05/29-10:11:35.717181 179                               Options.ttl: 2592000
2025/05/29-10:11:35.717183 179          Options.periodic_compaction_seconds: 0
2025/05/29-10:11:35.717185 179                        Options.default_temperature: kUnknown
2025/05/29-10:11:35.717186 179  Options.preclude_last_level_data_seconds: 0
2025/05/29-10:11:35.717188 179    Options.preserve_internal_time_seconds: 0
2025/05/29-10:11:35.717190 179                       Options.enable_blob_files: false
2025/05/29-10:11:35.717192 179                           Options.min_blob_size: 0
2025/05/29-10:11:35.717196 179                          Options.blob_file_size: 268435456
2025/05/29-10:11:35.717198 179                   Options.blob_compression_type: NoCompression
2025/05/29-10:11:35.717200 179          Options.enable_blob_garbage_collection: false
2025/05/29-10:11:35.717202 179      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/29-10:11:35.717204 179 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/29-10:11:35.717206 179          Options.blob_compaction_readahead_size: 0
2025/05/29-10:11:35.717208 179                Options.blob_file_starting_level: 0
2025/05/29-10:11:35.717210 179         Options.experimental_mempurge_threshold: 0.000000
2025/05/29-10:11:35.717212 179            Options.memtable_max_range_deletions: 0
2025/05/29-10:11:35.722358 179               Options.comparator: leveldb.BytewiseComparator
2025/05/29-10:11:35.722363 179           Options.merge_operator: None
2025/05/29-10:11:35.722365 179        Options.compaction_filter: None
2025/05/29-10:11:35.722368 179        Options.compaction_filter_factory: None
2025/05/29-10:11:35.722370 179  Options.sst_partitioner_factory: None
2025/05/29-10:11:35.722373 179         Options.memtable_factory: SkipListFactory
2025/05/29-10:11:35.722375 179            Options.table_factory: BlockBasedTable
2025/05/29-10:11:35.722400 179            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7be3eee00ce0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7be3eee2a010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/29-10:11:35.722404 179        Options.write_buffer_size: 10485760
2025/05/29-10:11:35.722405 179  Options.max_write_buffer_number: 2
2025/05/29-10:11:35.722407 179          Options.compression: LZ4
2025/05/29-10:11:35.722409 179                  Options.bottommost_compression: Disabled
2025/05/29-10:11:35.722411 179       Options.prefix_extractor: nullptr
2025/05/29-10:11:35.722413 179   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/29-10:11:35.722414 179             Options.num_levels: 7
2025/05/29-10:11:35.722416 179        Options.min_write_buffer_number_to_merge: 1
2025/05/29-10:11:35.722424 179     Options.max_write_buffer_number_to_maintain: 0
2025/05/29-10:11:35.722425 179     Options.max_write_buffer_size_to_maintain: 0
2025/05/29-10:11:35.722427 179            Options.bottommost_compression_opts.window_bits: -14
2025/05/29-10:11:35.722432 179                  Options.bottommost_compression_opts.level: 32767
2025/05/29-10:11:35.722434 179               Options.bottommost_compression_opts.strategy: 0
2025/05/29-10:11:35.722435 179         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.722437 179         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.722439 179         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/29-10:11:35.722441 179                  Options.bottommost_compression_opts.enabled: false
2025/05/29-10:11:35.722445 179         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.722448 179         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.722449 179            Options.compression_opts.window_bits: -14
2025/05/29-10:11:35.722453 179                  Options.compression_opts.level: 32767
2025/05/29-10:11:35.722456 179               Options.compression_opts.strategy: 0
2025/05/29-10:11:35.722458 179         Options.compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.722461 179         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.722464 179         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.722473 179         Options.compression_opts.parallel_threads: 1
2025/05/29-10:11:35.722475 179                  Options.compression_opts.enabled: false
2025/05/29-10:11:35.722477 179         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.722479 179      Options.level0_file_num_compaction_trigger: 4
2025/05/29-10:11:35.722481 179          Options.level0_slowdown_writes_trigger: 20
2025/05/29-10:11:35.722482 179              Options.level0_stop_writes_trigger: 36
2025/05/29-10:11:35.722484 179                   Options.target_file_size_base: 67108864
2025/05/29-10:11:35.722486 179             Options.target_file_size_multiplier: 1
2025/05/29-10:11:35.722487 179                Options.max_bytes_for_level_base: 268435456
2025/05/29-10:11:35.722489 179 Options.level_compaction_dynamic_level_bytes: 1
2025/05/29-10:11:35.722492 179          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/29-10:11:35.722494 179 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/29-10:11:35.722496 179 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/29-10:11:35.722497 179 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/29-10:11:35.722499 179 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/29-10:11:35.722500 179 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/29-10:11:35.722502 179 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/29-10:11:35.722510 179 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/29-10:11:35.722511 179       Options.max_sequential_skip_in_iterations: 8
2025/05/29-10:11:35.722513 179                    Options.max_compaction_bytes: 1677721600
2025/05/29-10:11:35.722515 179                        Options.arena_block_size: 1048576
2025/05/29-10:11:35.722517 179   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/29-10:11:35.722520 179   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/29-10:11:35.722522 179                Options.disable_auto_compactions: 0
2025/05/29-10:11:35.722524 179                        Options.compaction_style: kCompactionStyleLevel
2025/05/29-10:11:35.722526 179                          Options.compaction_pri: kMinOverlappingRatio
2025/05/29-10:11:35.722528 179 Options.compaction_options_universal.size_ratio: 1
2025/05/29-10:11:35.722529 179 Options.compaction_options_universal.min_merge_width: 2
2025/05/29-10:11:35.722531 179 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/29-10:11:35.722533 179 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/29-10:11:35.722535 179 Options.compaction_options_universal.compression_size_percent: -1
2025/05/29-10:11:35.722536 179 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/29-10:11:35.722538 179 Options.compaction_options_universal.max_read_amp: -1
2025/05/29-10:11:35.722540 179 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/29-10:11:35.722544 179 Options.compaction_options_fifo.allow_compaction: 0
2025/05/29-10:11:35.722550 179                   Options.table_properties_collectors: 
2025/05/29-10:11:35.722552 179                   Options.inplace_update_support: 0
2025/05/29-10:11:35.722554 179                 Options.inplace_update_num_locks: 10000
2025/05/29-10:11:35.722556 179               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/29-10:11:35.722558 179               Options.memtable_whole_key_filtering: 0
2025/05/29-10:11:35.722573 179   Options.memtable_huge_page_size: 0
2025/05/29-10:11:35.722576 179                           Options.bloom_locality: 0
2025/05/29-10:11:35.722578 179                    Options.max_successive_merges: 0
2025/05/29-10:11:35.722579 179             Options.strict_max_successive_merges: 0
2025/05/29-10:11:35.722581 179                Options.optimize_filters_for_hits: 0
2025/05/29-10:11:35.722582 179                Options.paranoid_file_checks: 0
2025/05/29-10:11:35.722584 179                Options.force_consistency_checks: 1
2025/05/29-10:11:35.722587 179                Options.report_bg_io_stats: 0
2025/05/29-10:11:35.722589 179                               Options.ttl: 2592000
2025/05/29-10:11:35.722590 179          Options.periodic_compaction_seconds: 0
2025/05/29-10:11:35.722592 179                        Options.default_temperature: kUnknown
2025/05/29-10:11:35.722594 179  Options.preclude_last_level_data_seconds: 0
2025/05/29-10:11:35.722595 179    Options.preserve_internal_time_seconds: 0
2025/05/29-10:11:35.722598 179                       Options.enable_blob_files: false
2025/05/29-10:11:35.722600 179                           Options.min_blob_size: 0
2025/05/29-10:11:35.722601 179                          Options.blob_file_size: 268435456
2025/05/29-10:11:35.722603 179                   Options.blob_compression_type: NoCompression
2025/05/29-10:11:35.722605 179          Options.enable_blob_garbage_collection: false
2025/05/29-10:11:35.722606 179      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/29-10:11:35.722608 179 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/29-10:11:35.722610 179          Options.blob_compaction_readahead_size: 0
2025/05/29-10:11:35.722612 179                Options.blob_file_starting_level: 0
2025/05/29-10:11:35.722613 179         Options.experimental_mempurge_threshold: 0.000000
2025/05/29-10:11:35.722615 179            Options.memtable_max_range_deletions: 0
2025/05/29-10:11:35.741460 179 DB pointer 0x7be3eee52c00
