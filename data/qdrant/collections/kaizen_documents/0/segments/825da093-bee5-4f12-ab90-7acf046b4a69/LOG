2025/05/29-10:11:35.651363 176 RocksDB version: 9.9.3
2025/05/29-10:11:35.651501 176 Compile date 2024-12-05 01:25:31
2025/05/29-10:11:35.651508 176 DB SUMMARY
2025/05/29-10:11:35.651512 176 Host name (Env):  beb888368ddf
2025/05/29-10:11:35.651514 176 DB Session ID:  Z3WRR5PFQNLDVUSNLKZU
2025/05/29-10:11:35.651547 176 SST files in ./storage/collections/kaizen_documents/0/segments/825da093-bee5-4f12-ab90-7acf046b4a69 dir, Total Num: 0, files: 
2025/05/29-10:11:35.651549 176 Write Ahead Log file in ./storage/collections/kaizen_documents/0/segments/825da093-bee5-4f12-ab90-7acf046b4a69: 
2025/05/29-10:11:35.651551 176                         Options.error_if_exists: 0
2025/05/29-10:11:35.651553 176                       Options.create_if_missing: 1
2025/05/29-10:11:35.651556 176                         Options.paranoid_checks: 1
2025/05/29-10:11:35.651558 176             Options.flush_verify_memtable_count: 1
2025/05/29-10:11:35.651561 176          Options.compaction_verify_record_count: 1
2025/05/29-10:11:35.651564 176                               Options.track_and_verify_wals_in_manifest: 0
2025/05/29-10:11:35.651566 176        Options.verify_sst_unique_id_in_manifest: 1
2025/05/29-10:11:35.651569 176                                     Options.env: 0x7be3ef622000
2025/05/29-10:11:35.651571 176                                      Options.fs: PosixFileSystem
2025/05/29-10:11:35.651573 176                                Options.info_log: 0x7be3f0a86000
2025/05/29-10:11:35.651576 176                Options.max_file_opening_threads: 16
2025/05/29-10:11:35.651578 176                              Options.statistics: (nil)
2025/05/29-10:11:35.651581 176                               Options.use_fsync: 0
2025/05/29-10:11:35.651583 176                       Options.max_log_file_size: 1048576
2025/05/29-10:11:35.651585 176                  Options.max_manifest_file_size: 1073741824
2025/05/29-10:11:35.651587 176                   Options.log_file_time_to_roll: 0
2025/05/29-10:11:35.651589 176                       Options.keep_log_file_num: 1
2025/05/29-10:11:35.651595 176                    Options.recycle_log_file_num: 0
2025/05/29-10:11:35.651597 176                         Options.allow_fallocate: 1
2025/05/29-10:11:35.651599 176                        Options.allow_mmap_reads: 0
2025/05/29-10:11:35.651601 176                       Options.allow_mmap_writes: 0
2025/05/29-10:11:35.651603 176                        Options.use_direct_reads: 0
2025/05/29-10:11:35.651607 176                        Options.use_direct_io_for_flush_and_compaction: 0
2025/05/29-10:11:35.651608 176          Options.create_missing_column_families: 1
2025/05/29-10:11:35.651611 176                              Options.db_log_dir: 
2025/05/29-10:11:35.651613 176                                 Options.wal_dir: 
2025/05/29-10:11:35.651620 176                Options.table_cache_numshardbits: 6
2025/05/29-10:11:35.651622 176                         Options.WAL_ttl_seconds: 0
2025/05/29-10:11:35.651623 176                       Options.WAL_size_limit_MB: 0
2025/05/29-10:11:35.651626 176                        Options.max_write_batch_group_size_bytes: 1048576
2025/05/29-10:11:35.651628 176             Options.manifest_preallocation_size: 4194304
2025/05/29-10:11:35.651632 176                     Options.is_fd_close_on_exec: 1
2025/05/29-10:11:35.651635 176                   Options.advise_random_on_open: 1
2025/05/29-10:11:35.651643 176                    Options.db_write_buffer_size: 0
2025/05/29-10:11:35.651672 176                    Options.write_buffer_manager: 0x7be3f0a04e80
2025/05/29-10:11:35.651674 176           Options.random_access_max_buffer_size: 1048576
2025/05/29-10:11:35.651689 176                      Options.use_adaptive_mutex: 0
2025/05/29-10:11:35.651693 176                            Options.rate_limiter: (nil)
2025/05/29-10:11:35.651695 176     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/05/29-10:11:35.651697 176                       Options.wal_recovery_mode: 0
2025/05/29-10:11:35.651705 176                  Options.enable_thread_tracking: 0
2025/05/29-10:11:35.651717 176                  Options.enable_pipelined_write: 0
2025/05/29-10:11:35.651721 176                  Options.unordered_write: 0
2025/05/29-10:11:35.651723 176         Options.allow_concurrent_memtable_write: 1
2025/05/29-10:11:35.651724 176      Options.enable_write_thread_adaptive_yield: 1
2025/05/29-10:11:35.651727 176             Options.write_thread_max_yield_usec: 100
2025/05/29-10:11:35.651729 176            Options.write_thread_slow_yield_usec: 3
2025/05/29-10:11:35.651731 176                               Options.row_cache: None
2025/05/29-10:11:35.651732 176                              Options.wal_filter: None
2025/05/29-10:11:35.651734 176             Options.avoid_flush_during_recovery: 0
2025/05/29-10:11:35.651736 176             Options.allow_ingest_behind: 0
2025/05/29-10:11:35.651737 176             Options.two_write_queues: 0
2025/05/29-10:11:35.651739 176             Options.manual_wal_flush: 0
2025/05/29-10:11:35.651743 176             Options.wal_compression: 0
2025/05/29-10:11:35.651752 176             Options.background_close_inactive_wals: 0
2025/05/29-10:11:35.651753 176             Options.atomic_flush: 0
2025/05/29-10:11:35.651757 176             Options.avoid_unnecessary_blocking_io: 0
2025/05/29-10:11:35.651763 176             Options.prefix_seek_opt_in_only: 0
2025/05/29-10:11:35.651764 176                 Options.persist_stats_to_disk: 0
2025/05/29-10:11:35.651767 176                 Options.write_dbid_to_manifest: 1
2025/05/29-10:11:35.651769 176                 Options.write_identity_file: 1
2025/05/29-10:11:35.651773 176                 Options.log_readahead_size: 0
2025/05/29-10:11:35.651798 176                 Options.file_checksum_gen_factory: Unknown
2025/05/29-10:11:35.651807 176                 Options.best_efforts_recovery: 0
2025/05/29-10:11:35.651810 176                Options.max_bgerror_resume_count: 2147483647
2025/05/29-10:11:35.651814 176            Options.bgerror_resume_retry_interval: 1000000
2025/05/29-10:11:35.651823 176             Options.allow_data_in_errors: 0
2025/05/29-10:11:35.651831 176             Options.db_host_id: __hostname__
2025/05/29-10:11:35.651833 176             Options.enforce_single_del_contracts: true
2025/05/29-10:11:35.651869 176             Options.metadata_write_temperature: kUnknown
2025/05/29-10:11:35.651871 176             Options.wal_write_temperature: kUnknown
2025/05/29-10:11:35.651873 176             Options.max_background_jobs: 2
2025/05/29-10:11:35.651875 176             Options.max_background_compactions: -1
2025/05/29-10:11:35.651878 176             Options.max_subcompactions: 1
2025/05/29-10:11:35.651886 176             Options.avoid_flush_during_shutdown: 0
2025/05/29-10:11:35.651894 176           Options.writable_file_max_buffer_size: 1048576
2025/05/29-10:11:35.651902 176             Options.delayed_write_rate : 16777216
2025/05/29-10:11:35.651904 176             Options.max_total_wal_size: 0
2025/05/29-10:11:35.651913 176             Options.delete_obsolete_files_period_micros: 180000000
2025/05/29-10:11:35.651921 176                   Options.stats_dump_period_sec: 600
2025/05/29-10:11:35.651926 176                 Options.stats_persist_period_sec: 600
2025/05/29-10:11:35.651928 176                 Options.stats_history_buffer_size: 1048576
2025/05/29-10:11:35.651929 176                          Options.max_open_files: 256
2025/05/29-10:11:35.651931 176                          Options.bytes_per_sync: 0
2025/05/29-10:11:35.651939 176                      Options.wal_bytes_per_sync: 0
2025/05/29-10:11:35.651942 176                   Options.strict_bytes_per_sync: 0
2025/05/29-10:11:35.651943 176       Options.compaction_readahead_size: 2097152
2025/05/29-10:11:35.651945 176                  Options.max_background_flushes: -1
2025/05/29-10:11:35.651947 176 Options.daily_offpeak_time_utc: 
2025/05/29-10:11:35.651954 176 Compression algorithms supported:
2025/05/29-10:11:35.651962 176 	kZSTD supported: 0
2025/05/29-10:11:35.651965 176 	kXpressCompression supported: 0
2025/05/29-10:11:35.651967 176 	kBZip2Compression supported: 0
2025/05/29-10:11:35.651973 176 	kZSTDNotFinalCompression supported: 0
2025/05/29-10:11:35.651980 176 	kLZ4Compression supported: 1
2025/05/29-10:11:35.651986 176 	kZlibCompression supported: 0
2025/05/29-10:11:35.651988 176 	kLZ4HCCompression supported: 1
2025/05/29-10:11:35.651998 176 	kSnappyCompression supported: 1
2025/05/29-10:11:35.652011 176 Fast CRC32 supported: Not supported on x86
2025/05/29-10:11:35.652014 176 DMutex implementation: pthread_mutex_t
2025/05/29-10:11:35.652016 176 Jemalloc supported: 0
2025/05/29-10:11:35.674497 176               Options.comparator: leveldb.BytewiseComparator
2025/05/29-10:11:35.674502 176           Options.merge_operator: None
2025/05/29-10:11:35.674504 176        Options.compaction_filter: None
2025/05/29-10:11:35.674506 176        Options.compaction_filter_factory: None
2025/05/29-10:11:35.674508 176  Options.sst_partitioner_factory: None
2025/05/29-10:11:35.674510 176         Options.memtable_factory: SkipListFactory
2025/05/29-10:11:35.674512 176            Options.table_factory: BlockBasedTable
2025/05/29-10:11:35.674608 176            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7be3f0a00ce0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7be3f0a2a010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/29-10:11:35.674615 176        Options.write_buffer_size: 10485760
2025/05/29-10:11:35.674618 176  Options.max_write_buffer_number: 2
2025/05/29-10:11:35.674620 176          Options.compression: LZ4
2025/05/29-10:11:35.674624 176                  Options.bottommost_compression: Disabled
2025/05/29-10:11:35.674626 176       Options.prefix_extractor: nullptr
2025/05/29-10:11:35.674627 176   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/29-10:11:35.674630 176             Options.num_levels: 7
2025/05/29-10:11:35.674634 176        Options.min_write_buffer_number_to_merge: 1
2025/05/29-10:11:35.674636 176     Options.max_write_buffer_number_to_maintain: 0
2025/05/29-10:11:35.674637 176     Options.max_write_buffer_size_to_maintain: 0
2025/05/29-10:11:35.674639 176            Options.bottommost_compression_opts.window_bits: -14
2025/05/29-10:11:35.674643 176                  Options.bottommost_compression_opts.level: 32767
2025/05/29-10:11:35.674646 176               Options.bottommost_compression_opts.strategy: 0
2025/05/29-10:11:35.674647 176         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.674650 176         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.674674 176         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/29-10:11:35.674677 176                  Options.bottommost_compression_opts.enabled: false
2025/05/29-10:11:35.674678 176         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.674681 176         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.674683 176            Options.compression_opts.window_bits: -14
2025/05/29-10:11:35.674685 176                  Options.compression_opts.level: 32767
2025/05/29-10:11:35.674688 176               Options.compression_opts.strategy: 0
2025/05/29-10:11:35.674693 176         Options.compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.674696 176         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.674697 176         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.674699 176         Options.compression_opts.parallel_threads: 1
2025/05/29-10:11:35.674700 176                  Options.compression_opts.enabled: false
2025/05/29-10:11:35.674702 176         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.674722 176      Options.level0_file_num_compaction_trigger: 4
2025/05/29-10:11:35.674724 176          Options.level0_slowdown_writes_trigger: 20
2025/05/29-10:11:35.674726 176              Options.level0_stop_writes_trigger: 36
2025/05/29-10:11:35.674730 176                   Options.target_file_size_base: 67108864
2025/05/29-10:11:35.674731 176             Options.target_file_size_multiplier: 1
2025/05/29-10:11:35.674733 176                Options.max_bytes_for_level_base: 268435456
2025/05/29-10:11:35.674735 176 Options.level_compaction_dynamic_level_bytes: 1
2025/05/29-10:11:35.674739 176          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/29-10:11:35.674756 176 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/29-10:11:35.674759 176 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/29-10:11:35.674761 176 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/29-10:11:35.674762 176 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/29-10:11:35.674764 176 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/29-10:11:35.674767 176 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/29-10:11:35.674773 176 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/29-10:11:35.674789 176       Options.max_sequential_skip_in_iterations: 8
2025/05/29-10:11:35.674792 176                    Options.max_compaction_bytes: 1677721600
2025/05/29-10:11:35.674795 176                        Options.arena_block_size: 1048576
2025/05/29-10:11:35.674800 176   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/29-10:11:35.674804 176   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/29-10:11:35.674807 176                Options.disable_auto_compactions: 0
2025/05/29-10:11:35.674832 176                        Options.compaction_style: kCompactionStyleLevel
2025/05/29-10:11:35.674835 176                          Options.compaction_pri: kMinOverlappingRatio
2025/05/29-10:11:35.674839 176 Options.compaction_options_universal.size_ratio: 1
2025/05/29-10:11:35.674842 176 Options.compaction_options_universal.min_merge_width: 2
2025/05/29-10:11:35.674844 176 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/29-10:11:35.674857 176 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/29-10:11:35.674860 176 Options.compaction_options_universal.compression_size_percent: -1
2025/05/29-10:11:35.674862 176 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/29-10:11:35.674863 176 Options.compaction_options_universal.max_read_amp: -1
2025/05/29-10:11:35.674865 176 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/29-10:11:35.674867 176 Options.compaction_options_fifo.allow_compaction: 0
2025/05/29-10:11:35.674874 176                   Options.table_properties_collectors: 
2025/05/29-10:11:35.674876 176                   Options.inplace_update_support: 0
2025/05/29-10:11:35.674888 176                 Options.inplace_update_num_locks: 10000
2025/05/29-10:11:35.674890 176               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/29-10:11:35.674893 176               Options.memtable_whole_key_filtering: 0
2025/05/29-10:11:35.674895 176   Options.memtable_huge_page_size: 0
2025/05/29-10:11:35.674896 176                           Options.bloom_locality: 0
2025/05/29-10:11:35.674898 176                    Options.max_successive_merges: 0
2025/05/29-10:11:35.674899 176             Options.strict_max_successive_merges: 0
2025/05/29-10:11:35.674904 176                Options.optimize_filters_for_hits: 0
2025/05/29-10:11:35.674906 176                Options.paranoid_file_checks: 0
2025/05/29-10:11:35.674907 176                Options.force_consistency_checks: 1
2025/05/29-10:11:35.674908 176                Options.report_bg_io_stats: 0
2025/05/29-10:11:35.674910 176                               Options.ttl: 2592000
2025/05/29-10:11:35.674912 176          Options.periodic_compaction_seconds: 0
2025/05/29-10:11:35.674913 176                        Options.default_temperature: kUnknown
2025/05/29-10:11:35.674915 176  Options.preclude_last_level_data_seconds: 0
2025/05/29-10:11:35.674916 176    Options.preserve_internal_time_seconds: 0
2025/05/29-10:11:35.674918 176                       Options.enable_blob_files: false
2025/05/29-10:11:35.674919 176                           Options.min_blob_size: 0
2025/05/29-10:11:35.674921 176                          Options.blob_file_size: 268435456
2025/05/29-10:11:35.674923 176                   Options.blob_compression_type: NoCompression
2025/05/29-10:11:35.674924 176          Options.enable_blob_garbage_collection: false
2025/05/29-10:11:35.674926 176      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/29-10:11:35.674928 176 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/29-10:11:35.674930 176          Options.blob_compaction_readahead_size: 0
2025/05/29-10:11:35.674932 176                Options.blob_file_starting_level: 0
2025/05/29-10:11:35.674934 176         Options.experimental_mempurge_threshold: 0.000000
2025/05/29-10:11:35.674936 176            Options.memtable_max_range_deletions: 0
2025/05/29-10:11:35.697202 176               Options.comparator: leveldb.BytewiseComparator
2025/05/29-10:11:35.697207 176           Options.merge_operator: None
2025/05/29-10:11:35.697210 176        Options.compaction_filter: None
2025/05/29-10:11:35.697212 176        Options.compaction_filter_factory: None
2025/05/29-10:11:35.697214 176  Options.sst_partitioner_factory: None
2025/05/29-10:11:35.697216 176         Options.memtable_factory: SkipListFactory
2025/05/29-10:11:35.697217 176            Options.table_factory: BlockBasedTable
2025/05/29-10:11:35.697241 176            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7be3f0a00ce0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7be3f0a2a010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/29-10:11:35.697250 176        Options.write_buffer_size: 10485760
2025/05/29-10:11:35.697252 176  Options.max_write_buffer_number: 2
2025/05/29-10:11:35.697256 176          Options.compression: LZ4
2025/05/29-10:11:35.697259 176                  Options.bottommost_compression: Disabled
2025/05/29-10:11:35.697261 176       Options.prefix_extractor: nullptr
2025/05/29-10:11:35.697262 176   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/29-10:11:35.697264 176             Options.num_levels: 7
2025/05/29-10:11:35.697265 176        Options.min_write_buffer_number_to_merge: 1
2025/05/29-10:11:35.697267 176     Options.max_write_buffer_number_to_maintain: 0
2025/05/29-10:11:35.697268 176     Options.max_write_buffer_size_to_maintain: 0
2025/05/29-10:11:35.697270 176            Options.bottommost_compression_opts.window_bits: -14
2025/05/29-10:11:35.697272 176                  Options.bottommost_compression_opts.level: 32767
2025/05/29-10:11:35.697274 176               Options.bottommost_compression_opts.strategy: 0
2025/05/29-10:11:35.697275 176         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.697277 176         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.697279 176         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/29-10:11:35.697280 176                  Options.bottommost_compression_opts.enabled: false
2025/05/29-10:11:35.697282 176         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.697285 176         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.697287 176            Options.compression_opts.window_bits: -14
2025/05/29-10:11:35.697289 176                  Options.compression_opts.level: 32767
2025/05/29-10:11:35.697290 176               Options.compression_opts.strategy: 0
2025/05/29-10:11:35.697292 176         Options.compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.697293 176         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.697295 176         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.697298 176         Options.compression_opts.parallel_threads: 1
2025/05/29-10:11:35.697300 176                  Options.compression_opts.enabled: false
2025/05/29-10:11:35.697302 176         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.697304 176      Options.level0_file_num_compaction_trigger: 4
2025/05/29-10:11:35.697307 176          Options.level0_slowdown_writes_trigger: 20
2025/05/29-10:11:35.697308 176              Options.level0_stop_writes_trigger: 36
2025/05/29-10:11:35.697310 176                   Options.target_file_size_base: 67108864
2025/05/29-10:11:35.697312 176             Options.target_file_size_multiplier: 1
2025/05/29-10:11:35.697314 176                Options.max_bytes_for_level_base: 268435456
2025/05/29-10:11:35.697315 176 Options.level_compaction_dynamic_level_bytes: 1
2025/05/29-10:11:35.697318 176          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/29-10:11:35.697320 176 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/29-10:11:35.697323 176 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/29-10:11:35.697324 176 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/29-10:11:35.697326 176 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/29-10:11:35.697329 176 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/29-10:11:35.697331 176 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/29-10:11:35.697333 176 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/29-10:11:35.697337 176       Options.max_sequential_skip_in_iterations: 8
2025/05/29-10:11:35.697338 176                    Options.max_compaction_bytes: 1677721600
2025/05/29-10:11:35.697340 176                        Options.arena_block_size: 1048576
2025/05/29-10:11:35.697343 176   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/29-10:11:35.697345 176   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/29-10:11:35.697347 176                Options.disable_auto_compactions: 0
2025/05/29-10:11:35.697349 176                        Options.compaction_style: kCompactionStyleLevel
2025/05/29-10:11:35.697351 176                          Options.compaction_pri: kMinOverlappingRatio
2025/05/29-10:11:35.697353 176 Options.compaction_options_universal.size_ratio: 1
2025/05/29-10:11:35.697355 176 Options.compaction_options_universal.min_merge_width: 2
2025/05/29-10:11:35.697359 176 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/29-10:11:35.697361 176 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/29-10:11:35.697362 176 Options.compaction_options_universal.compression_size_percent: -1
2025/05/29-10:11:35.697365 176 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/29-10:11:35.697368 176 Options.compaction_options_universal.max_read_amp: -1
2025/05/29-10:11:35.697371 176 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/29-10:11:35.697374 176 Options.compaction_options_fifo.allow_compaction: 0
2025/05/29-10:11:35.697378 176                   Options.table_properties_collectors: 
2025/05/29-10:11:35.697380 176                   Options.inplace_update_support: 0
2025/05/29-10:11:35.697381 176                 Options.inplace_update_num_locks: 10000
2025/05/29-10:11:35.697383 176               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/29-10:11:35.697385 176               Options.memtable_whole_key_filtering: 0
2025/05/29-10:11:35.697387 176   Options.memtable_huge_page_size: 0
2025/05/29-10:11:35.697389 176                           Options.bloom_locality: 0
2025/05/29-10:11:35.697392 176                    Options.max_successive_merges: 0
2025/05/29-10:11:35.697393 176             Options.strict_max_successive_merges: 0
2025/05/29-10:11:35.697395 176                Options.optimize_filters_for_hits: 0
2025/05/29-10:11:35.697396 176                Options.paranoid_file_checks: 0
2025/05/29-10:11:35.697398 176                Options.force_consistency_checks: 1
2025/05/29-10:11:35.697400 176                Options.report_bg_io_stats: 0
2025/05/29-10:11:35.697401 176                               Options.ttl: 2592000
2025/05/29-10:11:35.697403 176          Options.periodic_compaction_seconds: 0
2025/05/29-10:11:35.697405 176                        Options.default_temperature: kUnknown
2025/05/29-10:11:35.697407 176  Options.preclude_last_level_data_seconds: 0
2025/05/29-10:11:35.697408 176    Options.preserve_internal_time_seconds: 0
2025/05/29-10:11:35.697410 176                       Options.enable_blob_files: false
2025/05/29-10:11:35.697413 176                           Options.min_blob_size: 0
2025/05/29-10:11:35.697414 176                          Options.blob_file_size: 268435456
2025/05/29-10:11:35.697418 176                   Options.blob_compression_type: NoCompression
2025/05/29-10:11:35.697420 176          Options.enable_blob_garbage_collection: false
2025/05/29-10:11:35.697422 176      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/29-10:11:35.697424 176 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/29-10:11:35.697426 176          Options.blob_compaction_readahead_size: 0
2025/05/29-10:11:35.697427 176                Options.blob_file_starting_level: 0
2025/05/29-10:11:35.697429 176         Options.experimental_mempurge_threshold: 0.000000
2025/05/29-10:11:35.697431 176            Options.memtable_max_range_deletions: 0
2025/05/29-10:11:35.702112 176               Options.comparator: leveldb.BytewiseComparator
2025/05/29-10:11:35.702117 176           Options.merge_operator: None
2025/05/29-10:11:35.702119 176        Options.compaction_filter: None
2025/05/29-10:11:35.702123 176        Options.compaction_filter_factory: None
2025/05/29-10:11:35.702125 176  Options.sst_partitioner_factory: None
2025/05/29-10:11:35.702127 176         Options.memtable_factory: SkipListFactory
2025/05/29-10:11:35.702136 176            Options.table_factory: BlockBasedTable
2025/05/29-10:11:35.702158 176            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7be3f0a00ce0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7be3f0a2a010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/29-10:11:35.702163 176        Options.write_buffer_size: 10485760
2025/05/29-10:11:35.702165 176  Options.max_write_buffer_number: 2
2025/05/29-10:11:35.702168 176          Options.compression: LZ4
2025/05/29-10:11:35.702170 176                  Options.bottommost_compression: Disabled
2025/05/29-10:11:35.702173 176       Options.prefix_extractor: nullptr
2025/05/29-10:11:35.702175 176   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/29-10:11:35.702176 176             Options.num_levels: 7
2025/05/29-10:11:35.702179 176        Options.min_write_buffer_number_to_merge: 1
2025/05/29-10:11:35.702182 176     Options.max_write_buffer_number_to_maintain: 0
2025/05/29-10:11:35.702185 176     Options.max_write_buffer_size_to_maintain: 0
2025/05/29-10:11:35.702188 176            Options.bottommost_compression_opts.window_bits: -14
2025/05/29-10:11:35.702192 176                  Options.bottommost_compression_opts.level: 32767
2025/05/29-10:11:35.702194 176               Options.bottommost_compression_opts.strategy: 0
2025/05/29-10:11:35.702196 176         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.702198 176         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.702199 176         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/29-10:11:35.702202 176                  Options.bottommost_compression_opts.enabled: false
2025/05/29-10:11:35.702204 176         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.702205 176         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.702207 176            Options.compression_opts.window_bits: -14
2025/05/29-10:11:35.702209 176                  Options.compression_opts.level: 32767
2025/05/29-10:11:35.702210 176               Options.compression_opts.strategy: 0
2025/05/29-10:11:35.702212 176         Options.compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.702215 176         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.702217 176         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.702218 176         Options.compression_opts.parallel_threads: 1
2025/05/29-10:11:35.702220 176                  Options.compression_opts.enabled: false
2025/05/29-10:11:35.702221 176         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.702223 176      Options.level0_file_num_compaction_trigger: 4
2025/05/29-10:11:35.702225 176          Options.level0_slowdown_writes_trigger: 20
2025/05/29-10:11:35.702227 176              Options.level0_stop_writes_trigger: 36
2025/05/29-10:11:35.702228 176                   Options.target_file_size_base: 67108864
2025/05/29-10:11:35.702230 176             Options.target_file_size_multiplier: 1
2025/05/29-10:11:35.702232 176                Options.max_bytes_for_level_base: 268435456
2025/05/29-10:11:35.702233 176 Options.level_compaction_dynamic_level_bytes: 1
2025/05/29-10:11:35.702237 176          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/29-10:11:35.702239 176 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/29-10:11:35.702241 176 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/29-10:11:35.702254 176 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/29-10:11:35.702256 176 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/29-10:11:35.702258 176 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/29-10:11:35.702261 176 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/29-10:11:35.702264 176 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/29-10:11:35.702266 176       Options.max_sequential_skip_in_iterations: 8
2025/05/29-10:11:35.702267 176                    Options.max_compaction_bytes: 1677721600
2025/05/29-10:11:35.702269 176                        Options.arena_block_size: 1048576
2025/05/29-10:11:35.702270 176   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/29-10:11:35.702272 176   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/29-10:11:35.702273 176                Options.disable_auto_compactions: 0
2025/05/29-10:11:35.702275 176                        Options.compaction_style: kCompactionStyleLevel
2025/05/29-10:11:35.702278 176                          Options.compaction_pri: kMinOverlappingRatio
2025/05/29-10:11:35.702280 176 Options.compaction_options_universal.size_ratio: 1
2025/05/29-10:11:35.702281 176 Options.compaction_options_universal.min_merge_width: 2
2025/05/29-10:11:35.702284 176 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/29-10:11:35.702285 176 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/29-10:11:35.702287 176 Options.compaction_options_universal.compression_size_percent: -1
2025/05/29-10:11:35.702289 176 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/29-10:11:35.702290 176 Options.compaction_options_universal.max_read_amp: -1
2025/05/29-10:11:35.702292 176 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/29-10:11:35.702293 176 Options.compaction_options_fifo.allow_compaction: 0
2025/05/29-10:11:35.702298 176                   Options.table_properties_collectors: 
2025/05/29-10:11:35.702300 176                   Options.inplace_update_support: 0
2025/05/29-10:11:35.702301 176                 Options.inplace_update_num_locks: 10000
2025/05/29-10:11:35.702303 176               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/29-10:11:35.702305 176               Options.memtable_whole_key_filtering: 0
2025/05/29-10:11:35.702306 176   Options.memtable_huge_page_size: 0
2025/05/29-10:11:35.702308 176                           Options.bloom_locality: 0
2025/05/29-10:11:35.702312 176                    Options.max_successive_merges: 0
2025/05/29-10:11:35.702315 176             Options.strict_max_successive_merges: 0
2025/05/29-10:11:35.702316 176                Options.optimize_filters_for_hits: 0
2025/05/29-10:11:35.702317 176                Options.paranoid_file_checks: 0
2025/05/29-10:11:35.702319 176                Options.force_consistency_checks: 1
2025/05/29-10:11:35.702320 176                Options.report_bg_io_stats: 0
2025/05/29-10:11:35.702322 176                               Options.ttl: 2592000
2025/05/29-10:11:35.702330 176          Options.periodic_compaction_seconds: 0
2025/05/29-10:11:35.702332 176                        Options.default_temperature: kUnknown
2025/05/29-10:11:35.702334 176  Options.preclude_last_level_data_seconds: 0
2025/05/29-10:11:35.702335 176    Options.preserve_internal_time_seconds: 0
2025/05/29-10:11:35.702337 176                       Options.enable_blob_files: false
2025/05/29-10:11:35.702338 176                           Options.min_blob_size: 0
2025/05/29-10:11:35.702340 176                          Options.blob_file_size: 268435456
2025/05/29-10:11:35.702341 176                   Options.blob_compression_type: NoCompression
2025/05/29-10:11:35.702343 176          Options.enable_blob_garbage_collection: false
2025/05/29-10:11:35.702345 176      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/29-10:11:35.702347 176 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/29-10:11:35.702350 176          Options.blob_compaction_readahead_size: 0
2025/05/29-10:11:35.702352 176                Options.blob_file_starting_level: 0
2025/05/29-10:11:35.702353 176         Options.experimental_mempurge_threshold: 0.000000
2025/05/29-10:11:35.702359 176            Options.memtable_max_range_deletions: 0
2025/05/29-10:11:35.707561 176               Options.comparator: leveldb.BytewiseComparator
2025/05/29-10:11:35.707566 176           Options.merge_operator: None
2025/05/29-10:11:35.707574 176        Options.compaction_filter: None
2025/05/29-10:11:35.707576 176        Options.compaction_filter_factory: None
2025/05/29-10:11:35.707578 176  Options.sst_partitioner_factory: None
2025/05/29-10:11:35.707580 176         Options.memtable_factory: SkipListFactory
2025/05/29-10:11:35.707581 176            Options.table_factory: BlockBasedTable
2025/05/29-10:11:35.707607 176            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7be3f0a00ce0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7be3f0a2a010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/29-10:11:35.707611 176        Options.write_buffer_size: 10485760
2025/05/29-10:11:35.707613 176  Options.max_write_buffer_number: 2
2025/05/29-10:11:35.707615 176          Options.compression: LZ4
2025/05/29-10:11:35.707617 176                  Options.bottommost_compression: Disabled
2025/05/29-10:11:35.707619 176       Options.prefix_extractor: nullptr
2025/05/29-10:11:35.707620 176   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/29-10:11:35.707622 176             Options.num_levels: 7
2025/05/29-10:11:35.707625 176        Options.min_write_buffer_number_to_merge: 1
2025/05/29-10:11:35.707627 176     Options.max_write_buffer_number_to_maintain: 0
2025/05/29-10:11:35.707630 176     Options.max_write_buffer_size_to_maintain: 0
2025/05/29-10:11:35.707631 176            Options.bottommost_compression_opts.window_bits: -14
2025/05/29-10:11:35.707634 176                  Options.bottommost_compression_opts.level: 32767
2025/05/29-10:11:35.707636 176               Options.bottommost_compression_opts.strategy: 0
2025/05/29-10:11:35.707638 176         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.707641 176         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.707643 176         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/29-10:11:35.707646 176                  Options.bottommost_compression_opts.enabled: false
2025/05/29-10:11:35.707649 176         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.707663 176         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.707665 176            Options.compression_opts.window_bits: -14
2025/05/29-10:11:35.707667 176                  Options.compression_opts.level: 32767
2025/05/29-10:11:35.707669 176               Options.compression_opts.strategy: 0
2025/05/29-10:11:35.707670 176         Options.compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.707672 176         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.707675 176         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.707677 176         Options.compression_opts.parallel_threads: 1
2025/05/29-10:11:35.707679 176                  Options.compression_opts.enabled: false
2025/05/29-10:11:35.707681 176         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.707683 176      Options.level0_file_num_compaction_trigger: 4
2025/05/29-10:11:35.707686 176          Options.level0_slowdown_writes_trigger: 20
2025/05/29-10:11:35.707687 176              Options.level0_stop_writes_trigger: 36
2025/05/29-10:11:35.707689 176                   Options.target_file_size_base: 67108864
2025/05/29-10:11:35.707690 176             Options.target_file_size_multiplier: 1
2025/05/29-10:11:35.707693 176                Options.max_bytes_for_level_base: 268435456
2025/05/29-10:11:35.707698 176 Options.level_compaction_dynamic_level_bytes: 1
2025/05/29-10:11:35.707702 176          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/29-10:11:35.707705 176 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/29-10:11:35.707706 176 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/29-10:11:35.707708 176 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/29-10:11:35.707710 176 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/29-10:11:35.707711 176 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/29-10:11:35.707713 176 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/29-10:11:35.707715 176 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/29-10:11:35.707718 176       Options.max_sequential_skip_in_iterations: 8
2025/05/29-10:11:35.707720 176                    Options.max_compaction_bytes: 1677721600
2025/05/29-10:11:35.707722 176                        Options.arena_block_size: 1048576
2025/05/29-10:11:35.707724 176   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/29-10:11:35.707727 176   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/29-10:11:35.707728 176                Options.disable_auto_compactions: 0
2025/05/29-10:11:35.707730 176                        Options.compaction_style: kCompactionStyleLevel
2025/05/29-10:11:35.707733 176                          Options.compaction_pri: kMinOverlappingRatio
2025/05/29-10:11:35.707736 176 Options.compaction_options_universal.size_ratio: 1
2025/05/29-10:11:35.707739 176 Options.compaction_options_universal.min_merge_width: 2
2025/05/29-10:11:35.707741 176 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/29-10:11:35.707742 176 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/29-10:11:35.707745 176 Options.compaction_options_universal.compression_size_percent: -1
2025/05/29-10:11:35.707747 176 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/29-10:11:35.707748 176 Options.compaction_options_universal.max_read_amp: -1
2025/05/29-10:11:35.707750 176 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/29-10:11:35.707751 176 Options.compaction_options_fifo.allow_compaction: 0
2025/05/29-10:11:35.707756 176                   Options.table_properties_collectors: 
2025/05/29-10:11:35.707759 176                   Options.inplace_update_support: 0
2025/05/29-10:11:35.707762 176                 Options.inplace_update_num_locks: 10000
2025/05/29-10:11:35.707764 176               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/29-10:11:35.707765 176               Options.memtable_whole_key_filtering: 0
2025/05/29-10:11:35.707768 176   Options.memtable_huge_page_size: 0
2025/05/29-10:11:35.707770 176                           Options.bloom_locality: 0
2025/05/29-10:11:35.707780 176                    Options.max_successive_merges: 0
2025/05/29-10:11:35.707783 176             Options.strict_max_successive_merges: 0
2025/05/29-10:11:35.707784 176                Options.optimize_filters_for_hits: 0
2025/05/29-10:11:35.707788 176                Options.paranoid_file_checks: 0
2025/05/29-10:11:35.707799 176                Options.force_consistency_checks: 1
2025/05/29-10:11:35.707864 176                Options.report_bg_io_stats: 0
2025/05/29-10:11:35.707866 176                               Options.ttl: 2592000
2025/05/29-10:11:35.707868 176          Options.periodic_compaction_seconds: 0
2025/05/29-10:11:35.707870 176                        Options.default_temperature: kUnknown
2025/05/29-10:11:35.707871 176  Options.preclude_last_level_data_seconds: 0
2025/05/29-10:11:35.707873 176    Options.preserve_internal_time_seconds: 0
2025/05/29-10:11:35.707875 176                       Options.enable_blob_files: false
2025/05/29-10:11:35.707877 176                           Options.min_blob_size: 0
2025/05/29-10:11:35.707878 176                          Options.blob_file_size: 268435456
2025/05/29-10:11:35.707880 176                   Options.blob_compression_type: NoCompression
2025/05/29-10:11:35.707882 176          Options.enable_blob_garbage_collection: false
2025/05/29-10:11:35.707884 176      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/29-10:11:35.707887 176 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/29-10:11:35.707889 176          Options.blob_compaction_readahead_size: 0
2025/05/29-10:11:35.707890 176                Options.blob_file_starting_level: 0
2025/05/29-10:11:35.707892 176         Options.experimental_mempurge_threshold: 0.000000
2025/05/29-10:11:35.707894 176            Options.memtable_max_range_deletions: 0
2025/05/29-10:11:35.720415 176               Options.comparator: leveldb.BytewiseComparator
2025/05/29-10:11:35.720419 176           Options.merge_operator: None
2025/05/29-10:11:35.720421 176        Options.compaction_filter: None
2025/05/29-10:11:35.720423 176        Options.compaction_filter_factory: None
2025/05/29-10:11:35.720424 176  Options.sst_partitioner_factory: None
2025/05/29-10:11:35.720426 176         Options.memtable_factory: SkipListFactory
2025/05/29-10:11:35.720427 176            Options.table_factory: BlockBasedTable
2025/05/29-10:11:35.720448 176            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7be3f0a00ce0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7be3f0a2a010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/29-10:11:35.720452 176        Options.write_buffer_size: 10485760
2025/05/29-10:11:35.720453 176  Options.max_write_buffer_number: 2
2025/05/29-10:11:35.720455 176          Options.compression: LZ4
2025/05/29-10:11:35.720457 176                  Options.bottommost_compression: Disabled
2025/05/29-10:11:35.720464 176       Options.prefix_extractor: nullptr
2025/05/29-10:11:35.720465 176   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/29-10:11:35.720469 176             Options.num_levels: 7
2025/05/29-10:11:35.720471 176        Options.min_write_buffer_number_to_merge: 1
2025/05/29-10:11:35.720472 176     Options.max_write_buffer_number_to_maintain: 0
2025/05/29-10:11:35.720474 176     Options.max_write_buffer_size_to_maintain: 0
2025/05/29-10:11:35.720476 176            Options.bottommost_compression_opts.window_bits: -14
2025/05/29-10:11:35.720478 176                  Options.bottommost_compression_opts.level: 32767
2025/05/29-10:11:35.720479 176               Options.bottommost_compression_opts.strategy: 0
2025/05/29-10:11:35.720481 176         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.720482 176         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.720484 176         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/29-10:11:35.720486 176                  Options.bottommost_compression_opts.enabled: false
2025/05/29-10:11:35.720487 176         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.720489 176         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.720490 176            Options.compression_opts.window_bits: -14
2025/05/29-10:11:35.720492 176                  Options.compression_opts.level: 32767
2025/05/29-10:11:35.720494 176               Options.compression_opts.strategy: 0
2025/05/29-10:11:35.720495 176         Options.compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.720497 176         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.720498 176         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.720500 176         Options.compression_opts.parallel_threads: 1
2025/05/29-10:11:35.720501 176                  Options.compression_opts.enabled: false
2025/05/29-10:11:35.720503 176         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.720505 176      Options.level0_file_num_compaction_trigger: 4
2025/05/29-10:11:35.720506 176          Options.level0_slowdown_writes_trigger: 20
2025/05/29-10:11:35.720508 176              Options.level0_stop_writes_trigger: 36
2025/05/29-10:11:35.720509 176                   Options.target_file_size_base: 67108864
2025/05/29-10:11:35.720511 176             Options.target_file_size_multiplier: 1
2025/05/29-10:11:35.720512 176                Options.max_bytes_for_level_base: 268435456
2025/05/29-10:11:35.720514 176 Options.level_compaction_dynamic_level_bytes: 1
2025/05/29-10:11:35.720518 176          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/29-10:11:35.720520 176 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/29-10:11:35.720522 176 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/29-10:11:35.720526 176 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/29-10:11:35.720527 176 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/29-10:11:35.720529 176 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/29-10:11:35.720530 176 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/29-10:11:35.720536 176 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/29-10:11:35.720537 176       Options.max_sequential_skip_in_iterations: 8
2025/05/29-10:11:35.720540 176                    Options.max_compaction_bytes: 1677721600
2025/05/29-10:11:35.720542 176                        Options.arena_block_size: 1048576
2025/05/29-10:11:35.720543 176   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/29-10:11:35.720545 176   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/29-10:11:35.720546 176                Options.disable_auto_compactions: 0
2025/05/29-10:11:35.720549 176                        Options.compaction_style: kCompactionStyleLevel
2025/05/29-10:11:35.720552 176                          Options.compaction_pri: kMinOverlappingRatio
2025/05/29-10:11:35.720553 176 Options.compaction_options_universal.size_ratio: 1
2025/05/29-10:11:35.720562 176 Options.compaction_options_universal.min_merge_width: 2
2025/05/29-10:11:35.720563 176 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/29-10:11:35.720567 176 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/29-10:11:35.720568 176 Options.compaction_options_universal.compression_size_percent: -1
2025/05/29-10:11:35.720572 176 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/29-10:11:35.720573 176 Options.compaction_options_universal.max_read_amp: -1
2025/05/29-10:11:35.720576 176 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/29-10:11:35.720578 176 Options.compaction_options_fifo.allow_compaction: 0
2025/05/29-10:11:35.720584 176                   Options.table_properties_collectors: 
2025/05/29-10:11:35.720587 176                   Options.inplace_update_support: 0
2025/05/29-10:11:35.720589 176                 Options.inplace_update_num_locks: 10000
2025/05/29-10:11:35.720590 176               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/29-10:11:35.720593 176               Options.memtable_whole_key_filtering: 0
2025/05/29-10:11:35.720595 176   Options.memtable_huge_page_size: 0
2025/05/29-10:11:35.720596 176                           Options.bloom_locality: 0
2025/05/29-10:11:35.720599 176                    Options.max_successive_merges: 0
2025/05/29-10:11:35.720602 176             Options.strict_max_successive_merges: 0
2025/05/29-10:11:35.720605 176                Options.optimize_filters_for_hits: 0
2025/05/29-10:11:35.720607 176                Options.paranoid_file_checks: 0
2025/05/29-10:11:35.720608 176                Options.force_consistency_checks: 1
2025/05/29-10:11:35.720610 176                Options.report_bg_io_stats: 0
2025/05/29-10:11:35.720613 176                               Options.ttl: 2592000
2025/05/29-10:11:35.720616 176          Options.periodic_compaction_seconds: 0
2025/05/29-10:11:35.720619 176                        Options.default_temperature: kUnknown
2025/05/29-10:11:35.720620 176  Options.preclude_last_level_data_seconds: 0
2025/05/29-10:11:35.720622 176    Options.preserve_internal_time_seconds: 0
2025/05/29-10:11:35.720624 176                       Options.enable_blob_files: false
2025/05/29-10:11:35.720625 176                           Options.min_blob_size: 0
2025/05/29-10:11:35.720627 176                          Options.blob_file_size: 268435456
2025/05/29-10:11:35.720630 176                   Options.blob_compression_type: NoCompression
2025/05/29-10:11:35.720631 176          Options.enable_blob_garbage_collection: false
2025/05/29-10:11:35.720633 176      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/29-10:11:35.720635 176 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/29-10:11:35.720637 176          Options.blob_compaction_readahead_size: 0
2025/05/29-10:11:35.720639 176                Options.blob_file_starting_level: 0
2025/05/29-10:11:35.720641 176         Options.experimental_mempurge_threshold: 0.000000
2025/05/29-10:11:35.720642 176            Options.memtable_max_range_deletions: 0
2025/05/29-10:11:35.728530 176 DB pointer 0x7be3f0a52c00
