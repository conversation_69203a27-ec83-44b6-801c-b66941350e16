2025/05/29-10:11:35.651417 178 RocksDB version: 9.9.3
2025/05/29-10:11:35.651536 178 Compile date 2024-12-05 01:25:31
2025/05/29-10:11:35.651540 178 DB SUMMARY
2025/05/29-10:11:35.651549 178 Host name (Env):  beb888368ddf
2025/05/29-10:11:35.651551 178 DB Session ID:  Z3WRR5PFQNLDVUSNLKZW
2025/05/29-10:11:35.651587 178 SST files in ./storage/collections/kaizen_documents/0/segments/d7725fa9-e421-45ad-aaac-7ce623c3ac30 dir, Total Num: 0, files: 
2025/05/29-10:11:35.651596 178 Write Ahead Log file in ./storage/collections/kaizen_documents/0/segments/d7725fa9-e421-45ad-aaac-7ce623c3ac30: 
2025/05/29-10:11:35.651598 178                         Options.error_if_exists: 0
2025/05/29-10:11:35.651600 178                       Options.create_if_missing: 1
2025/05/29-10:11:35.651616 178                         Options.paranoid_checks: 1
2025/05/29-10:11:35.651618 178             Options.flush_verify_memtable_count: 1
2025/05/29-10:11:35.651629 178          Options.compaction_verify_record_count: 1
2025/05/29-10:11:35.651631 178                               Options.track_and_verify_wals_in_manifest: 0
2025/05/29-10:11:35.651635 178        Options.verify_sst_unique_id_in_manifest: 1
2025/05/29-10:11:35.651637 178                                     Options.env: 0x7be3ef622000
2025/05/29-10:11:35.651643 178                                      Options.fs: PosixFileSystem
2025/05/29-10:11:35.651650 178                                Options.info_log: 0x7be3ef68f000
2025/05/29-10:11:35.651652 178                Options.max_file_opening_threads: 16
2025/05/29-10:11:35.651654 178                              Options.statistics: (nil)
2025/05/29-10:11:35.651659 178                               Options.use_fsync: 0
2025/05/29-10:11:35.651664 178                       Options.max_log_file_size: 1048576
2025/05/29-10:11:35.651689 178                  Options.max_manifest_file_size: 1073741824
2025/05/29-10:11:35.651704 178                   Options.log_file_time_to_roll: 0
2025/05/29-10:11:35.651726 178                       Options.keep_log_file_num: 1
2025/05/29-10:11:35.651731 178                    Options.recycle_log_file_num: 0
2025/05/29-10:11:35.651749 178                         Options.allow_fallocate: 1
2025/05/29-10:11:35.651753 178                        Options.allow_mmap_reads: 0
2025/05/29-10:11:35.651757 178                       Options.allow_mmap_writes: 0
2025/05/29-10:11:35.651777 178                        Options.use_direct_reads: 0
2025/05/29-10:11:35.651779 178                        Options.use_direct_io_for_flush_and_compaction: 0
2025/05/29-10:11:35.651789 178          Options.create_missing_column_families: 1
2025/05/29-10:11:35.651791 178                              Options.db_log_dir: 
2025/05/29-10:11:35.651797 178                                 Options.wal_dir: 
2025/05/29-10:11:35.651798 178                Options.table_cache_numshardbits: 6
2025/05/29-10:11:35.651815 178                         Options.WAL_ttl_seconds: 0
2025/05/29-10:11:35.651822 178                       Options.WAL_size_limit_MB: 0
2025/05/29-10:11:35.651829 178                        Options.max_write_batch_group_size_bytes: 1048576
2025/05/29-10:11:35.651833 178             Options.manifest_preallocation_size: 4194304
2025/05/29-10:11:35.651877 178                     Options.is_fd_close_on_exec: 1
2025/05/29-10:11:35.651883 178                   Options.advise_random_on_open: 1
2025/05/29-10:11:35.651885 178                    Options.db_write_buffer_size: 0
2025/05/29-10:11:35.651895 178                    Options.write_buffer_manager: 0x7be3ef604e80
2025/05/29-10:11:35.651901 178           Options.random_access_max_buffer_size: 1048576
2025/05/29-10:11:35.651903 178                      Options.use_adaptive_mutex: 0
2025/05/29-10:11:35.651905 178                            Options.rate_limiter: (nil)
2025/05/29-10:11:35.651914 178     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/05/29-10:11:35.651920 178                       Options.wal_recovery_mode: 0
2025/05/29-10:11:35.651925 178                  Options.enable_thread_tracking: 0
2025/05/29-10:11:35.651942 178                  Options.enable_pipelined_write: 0
2025/05/29-10:11:35.651948 178                  Options.unordered_write: 0
2025/05/29-10:11:35.651964 178         Options.allow_concurrent_memtable_write: 1
2025/05/29-10:11:35.651966 178      Options.enable_write_thread_adaptive_yield: 1
2025/05/29-10:11:35.651971 178             Options.write_thread_max_yield_usec: 100
2025/05/29-10:11:35.651973 178            Options.write_thread_slow_yield_usec: 3
2025/05/29-10:11:35.651980 178                               Options.row_cache: None
2025/05/29-10:11:35.651982 178                              Options.wal_filter: None
2025/05/29-10:11:35.651988 178             Options.avoid_flush_during_recovery: 0
2025/05/29-10:11:35.651991 178             Options.allow_ingest_behind: 0
2025/05/29-10:11:35.651996 178             Options.two_write_queues: 0
2025/05/29-10:11:35.651998 178             Options.manual_wal_flush: 0
2025/05/29-10:11:35.652025 178             Options.wal_compression: 0
2025/05/29-10:11:35.652040 178             Options.background_close_inactive_wals: 0
2025/05/29-10:11:35.652042 178             Options.atomic_flush: 0
2025/05/29-10:11:35.652045 178             Options.avoid_unnecessary_blocking_io: 0
2025/05/29-10:11:35.652063 178             Options.prefix_seek_opt_in_only: 0
2025/05/29-10:11:35.652065 178                 Options.persist_stats_to_disk: 0
2025/05/29-10:11:35.652067 178                 Options.write_dbid_to_manifest: 1
2025/05/29-10:11:35.652073 178                 Options.write_identity_file: 1
2025/05/29-10:11:35.652075 178                 Options.log_readahead_size: 0
2025/05/29-10:11:35.652077 178                 Options.file_checksum_gen_factory: Unknown
2025/05/29-10:11:35.652079 178                 Options.best_efforts_recovery: 0
2025/05/29-10:11:35.652089 178                Options.max_bgerror_resume_count: 2147483647
2025/05/29-10:11:35.652091 178            Options.bgerror_resume_retry_interval: 1000000
2025/05/29-10:11:35.652092 178             Options.allow_data_in_errors: 0
2025/05/29-10:11:35.652094 178             Options.db_host_id: __hostname__
2025/05/29-10:11:35.652095 178             Options.enforce_single_del_contracts: true
2025/05/29-10:11:35.652097 178             Options.metadata_write_temperature: kUnknown
2025/05/29-10:11:35.652099 178             Options.wal_write_temperature: kUnknown
2025/05/29-10:11:35.652101 178             Options.max_background_jobs: 2
2025/05/29-10:11:35.652102 178             Options.max_background_compactions: -1
2025/05/29-10:11:35.652105 178             Options.max_subcompactions: 1
2025/05/29-10:11:35.652106 178             Options.avoid_flush_during_shutdown: 0
2025/05/29-10:11:35.652110 178           Options.writable_file_max_buffer_size: 1048576
2025/05/29-10:11:35.652112 178             Options.delayed_write_rate : 16777216
2025/05/29-10:11:35.652115 178             Options.max_total_wal_size: 0
2025/05/29-10:11:35.652122 178             Options.delete_obsolete_files_period_micros: 180000000
2025/05/29-10:11:35.652123 178                   Options.stats_dump_period_sec: 600
2025/05/29-10:11:35.652125 178                 Options.stats_persist_period_sec: 600
2025/05/29-10:11:35.652128 178                 Options.stats_history_buffer_size: 1048576
2025/05/29-10:11:35.652130 178                          Options.max_open_files: 256
2025/05/29-10:11:35.652131 178                          Options.bytes_per_sync: 0
2025/05/29-10:11:35.652133 178                      Options.wal_bytes_per_sync: 0
2025/05/29-10:11:35.652134 178                   Options.strict_bytes_per_sync: 0
2025/05/29-10:11:35.652135 178       Options.compaction_readahead_size: 2097152
2025/05/29-10:11:35.652137 178                  Options.max_background_flushes: -1
2025/05/29-10:11:35.652139 178 Options.daily_offpeak_time_utc: 
2025/05/29-10:11:35.652141 178 Compression algorithms supported:
2025/05/29-10:11:35.652143 178 	kZSTD supported: 0
2025/05/29-10:11:35.652145 178 	kXpressCompression supported: 0
2025/05/29-10:11:35.652147 178 	kBZip2Compression supported: 0
2025/05/29-10:11:35.652149 178 	kZSTDNotFinalCompression supported: 0
2025/05/29-10:11:35.652153 178 	kLZ4Compression supported: 1
2025/05/29-10:11:35.652155 178 	kZlibCompression supported: 0
2025/05/29-10:11:35.652163 178 	kLZ4HCCompression supported: 1
2025/05/29-10:11:35.652165 178 	kSnappyCompression supported: 1
2025/05/29-10:11:35.652170 178 Fast CRC32 supported: Not supported on x86
2025/05/29-10:11:35.652172 178 DMutex implementation: pthread_mutex_t
2025/05/29-10:11:35.652173 178 Jemalloc supported: 0
2025/05/29-10:11:35.674519 178               Options.comparator: leveldb.BytewiseComparator
2025/05/29-10:11:35.674524 178           Options.merge_operator: None
2025/05/29-10:11:35.674526 178        Options.compaction_filter: None
2025/05/29-10:11:35.674528 178        Options.compaction_filter_factory: None
2025/05/29-10:11:35.674530 178  Options.sst_partitioner_factory: None
2025/05/29-10:11:35.674532 178         Options.memtable_factory: SkipListFactory
2025/05/29-10:11:35.674533 178            Options.table_factory: BlockBasedTable
2025/05/29-10:11:35.674604 178            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7be3ef600ce0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7be3ef63a010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/29-10:11:35.674612 178        Options.write_buffer_size: 10485760
2025/05/29-10:11:35.674613 178  Options.max_write_buffer_number: 2
2025/05/29-10:11:35.674617 178          Options.compression: LZ4
2025/05/29-10:11:35.674619 178                  Options.bottommost_compression: Disabled
2025/05/29-10:11:35.674620 178       Options.prefix_extractor: nullptr
2025/05/29-10:11:35.674622 178   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/29-10:11:35.674625 178             Options.num_levels: 7
2025/05/29-10:11:35.674626 178        Options.min_write_buffer_number_to_merge: 1
2025/05/29-10:11:35.674629 178     Options.max_write_buffer_number_to_maintain: 0
2025/05/29-10:11:35.674630 178     Options.max_write_buffer_size_to_maintain: 0
2025/05/29-10:11:35.674642 178            Options.bottommost_compression_opts.window_bits: -14
2025/05/29-10:11:35.674645 178                  Options.bottommost_compression_opts.level: 32767
2025/05/29-10:11:35.674647 178               Options.bottommost_compression_opts.strategy: 0
2025/05/29-10:11:35.674649 178         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.674650 178         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.674654 178         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/29-10:11:35.674656 178                  Options.bottommost_compression_opts.enabled: false
2025/05/29-10:11:35.674657 178         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.674659 178         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.674661 178            Options.compression_opts.window_bits: -14
2025/05/29-10:11:35.674663 178                  Options.compression_opts.level: 32767
2025/05/29-10:11:35.674664 178               Options.compression_opts.strategy: 0
2025/05/29-10:11:35.674669 178         Options.compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.674671 178         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.674674 178         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.674676 178         Options.compression_opts.parallel_threads: 1
2025/05/29-10:11:35.674677 178                  Options.compression_opts.enabled: false
2025/05/29-10:11:35.674679 178         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.674681 178      Options.level0_file_num_compaction_trigger: 4
2025/05/29-10:11:35.674683 178          Options.level0_slowdown_writes_trigger: 20
2025/05/29-10:11:35.674685 178              Options.level0_stop_writes_trigger: 36
2025/05/29-10:11:35.674688 178                   Options.target_file_size_base: 67108864
2025/05/29-10:11:35.674691 178             Options.target_file_size_multiplier: 1
2025/05/29-10:11:35.674693 178                Options.max_bytes_for_level_base: 268435456
2025/05/29-10:11:35.674694 178 Options.level_compaction_dynamic_level_bytes: 1
2025/05/29-10:11:35.674698 178          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/29-10:11:35.674701 178 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/29-10:11:35.674720 178 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/29-10:11:35.674726 178 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/29-10:11:35.674729 178 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/29-10:11:35.674730 178 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/29-10:11:35.674732 178 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/29-10:11:35.674734 178 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/29-10:11:35.674737 178       Options.max_sequential_skip_in_iterations: 8
2025/05/29-10:11:35.674739 178                    Options.max_compaction_bytes: 1677721600
2025/05/29-10:11:35.674744 178                        Options.arena_block_size: 1048576
2025/05/29-10:11:35.674745 178   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/29-10:11:35.674747 178   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/29-10:11:35.674749 178                Options.disable_auto_compactions: 0
2025/05/29-10:11:35.674752 178                        Options.compaction_style: kCompactionStyleLevel
2025/05/29-10:11:35.674754 178                          Options.compaction_pri: kMinOverlappingRatio
2025/05/29-10:11:35.674756 178 Options.compaction_options_universal.size_ratio: 1
2025/05/29-10:11:35.674758 178 Options.compaction_options_universal.min_merge_width: 2
2025/05/29-10:11:35.674760 178 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/29-10:11:35.674763 178 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/29-10:11:35.674765 178 Options.compaction_options_universal.compression_size_percent: -1
2025/05/29-10:11:35.674767 178 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/29-10:11:35.674792 178 Options.compaction_options_universal.max_read_amp: -1
2025/05/29-10:11:35.674794 178 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/29-10:11:35.674798 178 Options.compaction_options_fifo.allow_compaction: 0
2025/05/29-10:11:35.674802 178                   Options.table_properties_collectors: 
2025/05/29-10:11:35.674804 178                   Options.inplace_update_support: 0
2025/05/29-10:11:35.674808 178                 Options.inplace_update_num_locks: 10000
2025/05/29-10:11:35.674820 178               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/29-10:11:35.674822 178               Options.memtable_whole_key_filtering: 0
2025/05/29-10:11:35.674824 178   Options.memtable_huge_page_size: 0
2025/05/29-10:11:35.674826 178                           Options.bloom_locality: 0
2025/05/29-10:11:35.674827 178                    Options.max_successive_merges: 0
2025/05/29-10:11:35.674829 178             Options.strict_max_successive_merges: 0
2025/05/29-10:11:35.674834 178                Options.optimize_filters_for_hits: 0
2025/05/29-10:11:35.674837 178                Options.paranoid_file_checks: 0
2025/05/29-10:11:35.674838 178                Options.force_consistency_checks: 1
2025/05/29-10:11:35.674842 178                Options.report_bg_io_stats: 0
2025/05/29-10:11:35.674845 178                               Options.ttl: 2592000
2025/05/29-10:11:35.674849 178          Options.periodic_compaction_seconds: 0
2025/05/29-10:11:35.674851 178                        Options.default_temperature: kUnknown
2025/05/29-10:11:35.674852 178  Options.preclude_last_level_data_seconds: 0
2025/05/29-10:11:35.674854 178    Options.preserve_internal_time_seconds: 0
2025/05/29-10:11:35.674856 178                       Options.enable_blob_files: false
2025/05/29-10:11:35.674857 178                           Options.min_blob_size: 0
2025/05/29-10:11:35.674859 178                          Options.blob_file_size: 268435456
2025/05/29-10:11:35.674861 178                   Options.blob_compression_type: NoCompression
2025/05/29-10:11:35.674864 178          Options.enable_blob_garbage_collection: false
2025/05/29-10:11:35.674866 178      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/29-10:11:35.674868 178 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/29-10:11:35.674871 178          Options.blob_compaction_readahead_size: 0
2025/05/29-10:11:35.674873 178                Options.blob_file_starting_level: 0
2025/05/29-10:11:35.674875 178         Options.experimental_mempurge_threshold: 0.000000
2025/05/29-10:11:35.674901 178            Options.memtable_max_range_deletions: 0
2025/05/29-10:11:35.697267 178               Options.comparator: leveldb.BytewiseComparator
2025/05/29-10:11:35.697272 178           Options.merge_operator: None
2025/05/29-10:11:35.697274 178        Options.compaction_filter: None
2025/05/29-10:11:35.697276 178        Options.compaction_filter_factory: None
2025/05/29-10:11:35.697280 178  Options.sst_partitioner_factory: None
2025/05/29-10:11:35.697282 178         Options.memtable_factory: SkipListFactory
2025/05/29-10:11:35.697285 178            Options.table_factory: BlockBasedTable
2025/05/29-10:11:35.697311 178            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7be3ef600ce0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7be3ef63a010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/29-10:11:35.697316 178        Options.write_buffer_size: 10485760
2025/05/29-10:11:35.697318 178  Options.max_write_buffer_number: 2
2025/05/29-10:11:35.697320 178          Options.compression: LZ4
2025/05/29-10:11:35.697323 178                  Options.bottommost_compression: Disabled
2025/05/29-10:11:35.697328 178       Options.prefix_extractor: nullptr
2025/05/29-10:11:35.697330 178   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/29-10:11:35.697331 178             Options.num_levels: 7
2025/05/29-10:11:35.697333 178        Options.min_write_buffer_number_to_merge: 1
2025/05/29-10:11:35.697336 178     Options.max_write_buffer_number_to_maintain: 0
2025/05/29-10:11:35.697337 178     Options.max_write_buffer_size_to_maintain: 0
2025/05/29-10:11:35.697339 178            Options.bottommost_compression_opts.window_bits: -14
2025/05/29-10:11:35.697342 178                  Options.bottommost_compression_opts.level: 32767
2025/05/29-10:11:35.697344 178               Options.bottommost_compression_opts.strategy: 0
2025/05/29-10:11:35.697345 178         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.697347 178         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.697349 178         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/29-10:11:35.697350 178                  Options.bottommost_compression_opts.enabled: false
2025/05/29-10:11:35.697352 178         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.697354 178         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.697356 178            Options.compression_opts.window_bits: -14
2025/05/29-10:11:35.697360 178                  Options.compression_opts.level: 32767
2025/05/29-10:11:35.697362 178               Options.compression_opts.strategy: 0
2025/05/29-10:11:35.697365 178         Options.compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.697366 178         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.697368 178         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.697371 178         Options.compression_opts.parallel_threads: 1
2025/05/29-10:11:35.697373 178                  Options.compression_opts.enabled: false
2025/05/29-10:11:35.697374 178         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.697376 178      Options.level0_file_num_compaction_trigger: 4
2025/05/29-10:11:35.697379 178          Options.level0_slowdown_writes_trigger: 20
2025/05/29-10:11:35.697381 178              Options.level0_stop_writes_trigger: 36
2025/05/29-10:11:35.697382 178                   Options.target_file_size_base: 67108864
2025/05/29-10:11:35.697384 178             Options.target_file_size_multiplier: 1
2025/05/29-10:11:35.697387 178                Options.max_bytes_for_level_base: 268435456
2025/05/29-10:11:35.697388 178 Options.level_compaction_dynamic_level_bytes: 1
2025/05/29-10:11:35.697392 178          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/29-10:11:35.697394 178 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/29-10:11:35.697397 178 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/29-10:11:35.697399 178 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/29-10:11:35.697401 178 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/29-10:11:35.697402 178 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/29-10:11:35.697404 178 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/29-10:11:35.697406 178 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/29-10:11:35.697407 178       Options.max_sequential_skip_in_iterations: 8
2025/05/29-10:11:35.697409 178                    Options.max_compaction_bytes: 1677721600
2025/05/29-10:11:35.697411 178                        Options.arena_block_size: 1048576
2025/05/29-10:11:35.697413 178   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/29-10:11:35.697415 178   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/29-10:11:35.697418 178                Options.disable_auto_compactions: 0
2025/05/29-10:11:35.697421 178                        Options.compaction_style: kCompactionStyleLevel
2025/05/29-10:11:35.697426 178                          Options.compaction_pri: kMinOverlappingRatio
2025/05/29-10:11:35.697428 178 Options.compaction_options_universal.size_ratio: 1
2025/05/29-10:11:35.697430 178 Options.compaction_options_universal.min_merge_width: 2
2025/05/29-10:11:35.697432 178 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/29-10:11:35.697433 178 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/29-10:11:35.697435 178 Options.compaction_options_universal.compression_size_percent: -1
2025/05/29-10:11:35.697437 178 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/29-10:11:35.697439 178 Options.compaction_options_universal.max_read_amp: -1
2025/05/29-10:11:35.697441 178 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/29-10:11:35.697444 178 Options.compaction_options_fifo.allow_compaction: 0
2025/05/29-10:11:35.697448 178                   Options.table_properties_collectors: 
2025/05/29-10:11:35.697450 178                   Options.inplace_update_support: 0
2025/05/29-10:11:35.697452 178                 Options.inplace_update_num_locks: 10000
2025/05/29-10:11:35.697454 178               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/29-10:11:35.697457 178               Options.memtable_whole_key_filtering: 0
2025/05/29-10:11:35.697459 178   Options.memtable_huge_page_size: 0
2025/05/29-10:11:35.697461 178                           Options.bloom_locality: 0
2025/05/29-10:11:35.697462 178                    Options.max_successive_merges: 0
2025/05/29-10:11:35.697464 178             Options.strict_max_successive_merges: 0
2025/05/29-10:11:35.697466 178                Options.optimize_filters_for_hits: 0
2025/05/29-10:11:35.697467 178                Options.paranoid_file_checks: 0
2025/05/29-10:11:35.697469 178                Options.force_consistency_checks: 1
2025/05/29-10:11:35.697472 178                Options.report_bg_io_stats: 0
2025/05/29-10:11:35.697474 178                               Options.ttl: 2592000
2025/05/29-10:11:35.697476 178          Options.periodic_compaction_seconds: 0
2025/05/29-10:11:35.697478 178                        Options.default_temperature: kUnknown
2025/05/29-10:11:35.697481 178  Options.preclude_last_level_data_seconds: 0
2025/05/29-10:11:35.697482 178    Options.preserve_internal_time_seconds: 0
2025/05/29-10:11:35.697484 178                       Options.enable_blob_files: false
2025/05/29-10:11:35.697486 178                           Options.min_blob_size: 0
2025/05/29-10:11:35.697488 178                          Options.blob_file_size: 268435456
2025/05/29-10:11:35.697490 178                   Options.blob_compression_type: NoCompression
2025/05/29-10:11:35.697491 178          Options.enable_blob_garbage_collection: false
2025/05/29-10:11:35.697493 178      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/29-10:11:35.697495 178 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/29-10:11:35.697497 178          Options.blob_compaction_readahead_size: 0
2025/05/29-10:11:35.697498 178                Options.blob_file_starting_level: 0
2025/05/29-10:11:35.697500 178         Options.experimental_mempurge_threshold: 0.000000
2025/05/29-10:11:35.697501 178            Options.memtable_max_range_deletions: 0
2025/05/29-10:11:35.702119 178               Options.comparator: leveldb.BytewiseComparator
2025/05/29-10:11:35.702129 178           Options.merge_operator: None
2025/05/29-10:11:35.702132 178        Options.compaction_filter: None
2025/05/29-10:11:35.702134 178        Options.compaction_filter_factory: None
2025/05/29-10:11:35.702137 178  Options.sst_partitioner_factory: None
2025/05/29-10:11:35.702139 178         Options.memtable_factory: SkipListFactory
2025/05/29-10:11:35.702140 178            Options.table_factory: BlockBasedTable
2025/05/29-10:11:35.702164 178            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7be3ef600ce0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7be3ef63a010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/29-10:11:35.702173 178        Options.write_buffer_size: 10485760
2025/05/29-10:11:35.702174 178  Options.max_write_buffer_number: 2
2025/05/29-10:11:35.702177 178          Options.compression: LZ4
2025/05/29-10:11:35.702180 178                  Options.bottommost_compression: Disabled
2025/05/29-10:11:35.702181 178       Options.prefix_extractor: nullptr
2025/05/29-10:11:35.702183 178   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/29-10:11:35.702185 178             Options.num_levels: 7
2025/05/29-10:11:35.702187 178        Options.min_write_buffer_number_to_merge: 1
2025/05/29-10:11:35.702189 178     Options.max_write_buffer_number_to_maintain: 0
2025/05/29-10:11:35.702192 178     Options.max_write_buffer_size_to_maintain: 0
2025/05/29-10:11:35.702195 178            Options.bottommost_compression_opts.window_bits: -14
2025/05/29-10:11:35.702197 178                  Options.bottommost_compression_opts.level: 32767
2025/05/29-10:11:35.702199 178               Options.bottommost_compression_opts.strategy: 0
2025/05/29-10:11:35.702200 178         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.702203 178         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.702205 178         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/29-10:11:35.702207 178                  Options.bottommost_compression_opts.enabled: false
2025/05/29-10:11:35.702208 178         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.702210 178         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.702212 178            Options.compression_opts.window_bits: -14
2025/05/29-10:11:35.702214 178                  Options.compression_opts.level: 32767
2025/05/29-10:11:35.702216 178               Options.compression_opts.strategy: 0
2025/05/29-10:11:35.702218 178         Options.compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.702219 178         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.702221 178         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.702223 178         Options.compression_opts.parallel_threads: 1
2025/05/29-10:11:35.702224 178                  Options.compression_opts.enabled: false
2025/05/29-10:11:35.702226 178         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.702228 178      Options.level0_file_num_compaction_trigger: 4
2025/05/29-10:11:35.702230 178          Options.level0_slowdown_writes_trigger: 20
2025/05/29-10:11:35.702231 178              Options.level0_stop_writes_trigger: 36
2025/05/29-10:11:35.702233 178                   Options.target_file_size_base: 67108864
2025/05/29-10:11:35.702236 178             Options.target_file_size_multiplier: 1
2025/05/29-10:11:35.702237 178                Options.max_bytes_for_level_base: 268435456
2025/05/29-10:11:35.702239 178 Options.level_compaction_dynamic_level_bytes: 1
2025/05/29-10:11:35.702242 178          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/29-10:11:35.702256 178 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/29-10:11:35.702258 178 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/29-10:11:35.702260 178 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/29-10:11:35.702261 178 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/29-10:11:35.702263 178 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/29-10:11:35.702265 178 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/29-10:11:35.702266 178 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/29-10:11:35.702268 178       Options.max_sequential_skip_in_iterations: 8
2025/05/29-10:11:35.702271 178                    Options.max_compaction_bytes: 1677721600
2025/05/29-10:11:35.702273 178                        Options.arena_block_size: 1048576
2025/05/29-10:11:35.702274 178   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/29-10:11:35.702277 178   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/29-10:11:35.702279 178                Options.disable_auto_compactions: 0
2025/05/29-10:11:35.702281 178                        Options.compaction_style: kCompactionStyleLevel
2025/05/29-10:11:35.702282 178                          Options.compaction_pri: kMinOverlappingRatio
2025/05/29-10:11:35.702284 178 Options.compaction_options_universal.size_ratio: 1
2025/05/29-10:11:35.702286 178 Options.compaction_options_universal.min_merge_width: 2
2025/05/29-10:11:35.702287 178 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/29-10:11:35.702289 178 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/29-10:11:35.702291 178 Options.compaction_options_universal.compression_size_percent: -1
2025/05/29-10:11:35.702293 178 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/29-10:11:35.702295 178 Options.compaction_options_universal.max_read_amp: -1
2025/05/29-10:11:35.702296 178 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/29-10:11:35.702298 178 Options.compaction_options_fifo.allow_compaction: 0
2025/05/29-10:11:35.702302 178                   Options.table_properties_collectors: 
2025/05/29-10:11:35.702304 178                   Options.inplace_update_support: 0
2025/05/29-10:11:35.702306 178                 Options.inplace_update_num_locks: 10000
2025/05/29-10:11:35.702308 178               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/29-10:11:35.702309 178               Options.memtable_whole_key_filtering: 0
2025/05/29-10:11:35.702311 178   Options.memtable_huge_page_size: 0
2025/05/29-10:11:35.702312 178                           Options.bloom_locality: 0
2025/05/29-10:11:35.702314 178                    Options.max_successive_merges: 0
2025/05/29-10:11:35.702315 178             Options.strict_max_successive_merges: 0
2025/05/29-10:11:35.702317 178                Options.optimize_filters_for_hits: 0
2025/05/29-10:11:35.702322 178                Options.paranoid_file_checks: 0
2025/05/29-10:11:35.702326 178                Options.force_consistency_checks: 1
2025/05/29-10:11:35.702327 178                Options.report_bg_io_stats: 0
2025/05/29-10:11:35.702328 178                               Options.ttl: 2592000
2025/05/29-10:11:35.702330 178          Options.periodic_compaction_seconds: 0
2025/05/29-10:11:35.702332 178                        Options.default_temperature: kUnknown
2025/05/29-10:11:35.702333 178  Options.preclude_last_level_data_seconds: 0
2025/05/29-10:11:35.702335 178    Options.preserve_internal_time_seconds: 0
2025/05/29-10:11:35.702336 178                       Options.enable_blob_files: false
2025/05/29-10:11:35.702339 178                           Options.min_blob_size: 0
2025/05/29-10:11:35.702340 178                          Options.blob_file_size: 268435456
2025/05/29-10:11:35.702342 178                   Options.blob_compression_type: NoCompression
2025/05/29-10:11:35.702343 178          Options.enable_blob_garbage_collection: false
2025/05/29-10:11:35.702347 178      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/29-10:11:35.702349 178 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/29-10:11:35.702351 178          Options.blob_compaction_readahead_size: 0
2025/05/29-10:11:35.702353 178                Options.blob_file_starting_level: 0
2025/05/29-10:11:35.702357 178         Options.experimental_mempurge_threshold: 0.000000
2025/05/29-10:11:35.702358 178            Options.memtable_max_range_deletions: 0
2025/05/29-10:11:35.707562 178               Options.comparator: leveldb.BytewiseComparator
2025/05/29-10:11:35.707566 178           Options.merge_operator: None
2025/05/29-10:11:35.707603 178        Options.compaction_filter: None
2025/05/29-10:11:35.707605 178        Options.compaction_filter_factory: None
2025/05/29-10:11:35.707606 178  Options.sst_partitioner_factory: None
2025/05/29-10:11:35.707608 178         Options.memtable_factory: SkipListFactory
2025/05/29-10:11:35.707610 178            Options.table_factory: BlockBasedTable
2025/05/29-10:11:35.707636 178            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7be3ef600ce0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7be3ef63a010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/29-10:11:35.707640 178        Options.write_buffer_size: 10485760
2025/05/29-10:11:35.707642 178  Options.max_write_buffer_number: 2
2025/05/29-10:11:35.707646 178          Options.compression: LZ4
2025/05/29-10:11:35.707648 178                  Options.bottommost_compression: Disabled
2025/05/29-10:11:35.707662 178       Options.prefix_extractor: nullptr
2025/05/29-10:11:35.707663 178   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/29-10:11:35.707666 178             Options.num_levels: 7
2025/05/29-10:11:35.707667 178        Options.min_write_buffer_number_to_merge: 1
2025/05/29-10:11:35.707669 178     Options.max_write_buffer_number_to_maintain: 0
2025/05/29-10:11:35.707670 178     Options.max_write_buffer_size_to_maintain: 0
2025/05/29-10:11:35.707672 178            Options.bottommost_compression_opts.window_bits: -14
2025/05/29-10:11:35.707674 178                  Options.bottommost_compression_opts.level: 32767
2025/05/29-10:11:35.707676 178               Options.bottommost_compression_opts.strategy: 0
2025/05/29-10:11:35.707678 178         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.707679 178         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.707682 178         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/29-10:11:35.707683 178                  Options.bottommost_compression_opts.enabled: false
2025/05/29-10:11:35.707686 178         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.707689 178         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.707690 178            Options.compression_opts.window_bits: -14
2025/05/29-10:11:35.707693 178                  Options.compression_opts.level: 32767
2025/05/29-10:11:35.707694 178               Options.compression_opts.strategy: 0
2025/05/29-10:11:35.707696 178         Options.compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.707697 178         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.707699 178         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.707700 178         Options.compression_opts.parallel_threads: 1
2025/05/29-10:11:35.707702 178                  Options.compression_opts.enabled: false
2025/05/29-10:11:35.707704 178         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.707707 178      Options.level0_file_num_compaction_trigger: 4
2025/05/29-10:11:35.707709 178          Options.level0_slowdown_writes_trigger: 20
2025/05/29-10:11:35.707711 178              Options.level0_stop_writes_trigger: 36
2025/05/29-10:11:35.707713 178                   Options.target_file_size_base: 67108864
2025/05/29-10:11:35.707716 178             Options.target_file_size_multiplier: 1
2025/05/29-10:11:35.707717 178                Options.max_bytes_for_level_base: 268435456
2025/05/29-10:11:35.707719 178 Options.level_compaction_dynamic_level_bytes: 1
2025/05/29-10:11:35.707721 178          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/29-10:11:35.707723 178 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/29-10:11:35.707726 178 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/29-10:11:35.707730 178 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/29-10:11:35.707732 178 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/29-10:11:35.707733 178 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/29-10:11:35.707736 178 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/29-10:11:35.707739 178 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/29-10:11:35.707741 178       Options.max_sequential_skip_in_iterations: 8
2025/05/29-10:11:35.707743 178                    Options.max_compaction_bytes: 1677721600
2025/05/29-10:11:35.707745 178                        Options.arena_block_size: 1048576
2025/05/29-10:11:35.707746 178   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/29-10:11:35.707748 178   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/29-10:11:35.707749 178                Options.disable_auto_compactions: 0
2025/05/29-10:11:35.707751 178                        Options.compaction_style: kCompactionStyleLevel
2025/05/29-10:11:35.707755 178                          Options.compaction_pri: kMinOverlappingRatio
2025/05/29-10:11:35.707756 178 Options.compaction_options_universal.size_ratio: 1
2025/05/29-10:11:35.707759 178 Options.compaction_options_universal.min_merge_width: 2
2025/05/29-10:11:35.707761 178 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/29-10:11:35.707762 178 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/29-10:11:35.707765 178 Options.compaction_options_universal.compression_size_percent: -1
2025/05/29-10:11:35.707767 178 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/29-10:11:35.707769 178 Options.compaction_options_universal.max_read_amp: -1
2025/05/29-10:11:35.707771 178 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/29-10:11:35.707773 178 Options.compaction_options_fifo.allow_compaction: 0
2025/05/29-10:11:35.707784 178                   Options.table_properties_collectors: 
2025/05/29-10:11:35.707787 178                   Options.inplace_update_support: 0
2025/05/29-10:11:35.707797 178                 Options.inplace_update_num_locks: 10000
2025/05/29-10:11:35.707827 178               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/29-10:11:35.707829 178               Options.memtable_whole_key_filtering: 0
2025/05/29-10:11:35.707831 178   Options.memtable_huge_page_size: 0
2025/05/29-10:11:35.707832 178                           Options.bloom_locality: 0
2025/05/29-10:11:35.707850 178                    Options.max_successive_merges: 0
2025/05/29-10:11:35.707852 178             Options.strict_max_successive_merges: 0
2025/05/29-10:11:35.707853 178                Options.optimize_filters_for_hits: 0
2025/05/29-10:11:35.707855 178                Options.paranoid_file_checks: 0
2025/05/29-10:11:35.707856 178                Options.force_consistency_checks: 1
2025/05/29-10:11:35.707858 178                Options.report_bg_io_stats: 0
2025/05/29-10:11:35.707860 178                               Options.ttl: 2592000
2025/05/29-10:11:35.707861 178          Options.periodic_compaction_seconds: 0
2025/05/29-10:11:35.707863 178                        Options.default_temperature: kUnknown
2025/05/29-10:11:35.707865 178  Options.preclude_last_level_data_seconds: 0
2025/05/29-10:11:35.707866 178    Options.preserve_internal_time_seconds: 0
2025/05/29-10:11:35.707868 178                       Options.enable_blob_files: false
2025/05/29-10:11:35.707870 178                           Options.min_blob_size: 0
2025/05/29-10:11:35.707872 178                          Options.blob_file_size: 268435456
2025/05/29-10:11:35.707874 178                   Options.blob_compression_type: NoCompression
2025/05/29-10:11:35.707875 178          Options.enable_blob_garbage_collection: false
2025/05/29-10:11:35.707878 178      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/29-10:11:35.707880 178 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/29-10:11:35.707882 178          Options.blob_compaction_readahead_size: 0
2025/05/29-10:11:35.707883 178                Options.blob_file_starting_level: 0
2025/05/29-10:11:35.707885 178         Options.experimental_mempurge_threshold: 0.000000
2025/05/29-10:11:35.707887 178            Options.memtable_max_range_deletions: 0
2025/05/29-10:11:35.720513 178               Options.comparator: leveldb.BytewiseComparator
2025/05/29-10:11:35.720519 178           Options.merge_operator: None
2025/05/29-10:11:35.720521 178        Options.compaction_filter: None
2025/05/29-10:11:35.720523 178        Options.compaction_filter_factory: None
2025/05/29-10:11:35.720525 178  Options.sst_partitioner_factory: None
2025/05/29-10:11:35.720528 178         Options.memtable_factory: SkipListFactory
2025/05/29-10:11:35.720530 178            Options.table_factory: BlockBasedTable
2025/05/29-10:11:35.720553 178            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7be3ef600ce0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7be3ef63a010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/29-10:11:35.720557 178        Options.write_buffer_size: 10485760
2025/05/29-10:11:35.720559 178  Options.max_write_buffer_number: 2
2025/05/29-10:11:35.720561 178          Options.compression: LZ4
2025/05/29-10:11:35.720563 178                  Options.bottommost_compression: Disabled
2025/05/29-10:11:35.720566 178       Options.prefix_extractor: nullptr
2025/05/29-10:11:35.720567 178   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/29-10:11:35.720575 178             Options.num_levels: 7
2025/05/29-10:11:35.720576 178        Options.min_write_buffer_number_to_merge: 1
2025/05/29-10:11:35.720578 178     Options.max_write_buffer_number_to_maintain: 0
2025/05/29-10:11:35.720583 178     Options.max_write_buffer_size_to_maintain: 0
2025/05/29-10:11:35.720586 178            Options.bottommost_compression_opts.window_bits: -14
2025/05/29-10:11:35.720588 178                  Options.bottommost_compression_opts.level: 32767
2025/05/29-10:11:35.720589 178               Options.bottommost_compression_opts.strategy: 0
2025/05/29-10:11:35.720591 178         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.720593 178         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.720595 178         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/29-10:11:35.720598 178                  Options.bottommost_compression_opts.enabled: false
2025/05/29-10:11:35.720599 178         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.720601 178         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.720602 178            Options.compression_opts.window_bits: -14
2025/05/29-10:11:35.720605 178                  Options.compression_opts.level: 32767
2025/05/29-10:11:35.720606 178               Options.compression_opts.strategy: 0
2025/05/29-10:11:35.720609 178         Options.compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.720629 178         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.720630 178         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.720632 178         Options.compression_opts.parallel_threads: 1
2025/05/29-10:11:35.720633 178                  Options.compression_opts.enabled: false
2025/05/29-10:11:35.720635 178         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.720638 178      Options.level0_file_num_compaction_trigger: 4
2025/05/29-10:11:35.720640 178          Options.level0_slowdown_writes_trigger: 20
2025/05/29-10:11:35.720641 178              Options.level0_stop_writes_trigger: 36
2025/05/29-10:11:35.720643 178                   Options.target_file_size_base: 67108864
2025/05/29-10:11:35.720645 178             Options.target_file_size_multiplier: 1
2025/05/29-10:11:35.720646 178                Options.max_bytes_for_level_base: 268435456
2025/05/29-10:11:35.720647 178 Options.level_compaction_dynamic_level_bytes: 1
2025/05/29-10:11:35.720650 178          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/29-10:11:35.720652 178 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/29-10:11:35.720658 178 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/29-10:11:35.720660 178 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/29-10:11:35.720661 178 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/29-10:11:35.720663 178 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/29-10:11:35.720664 178 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/29-10:11:35.720665 178 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/29-10:11:35.720667 178       Options.max_sequential_skip_in_iterations: 8
2025/05/29-10:11:35.720668 178                    Options.max_compaction_bytes: 1677721600
2025/05/29-10:11:35.720670 178                        Options.arena_block_size: 1048576
2025/05/29-10:11:35.720671 178   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/29-10:11:35.720673 178   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/29-10:11:35.720675 178                Options.disable_auto_compactions: 0
2025/05/29-10:11:35.720677 178                        Options.compaction_style: kCompactionStyleLevel
2025/05/29-10:11:35.720680 178                          Options.compaction_pri: kMinOverlappingRatio
2025/05/29-10:11:35.720681 178 Options.compaction_options_universal.size_ratio: 1
2025/05/29-10:11:35.720683 178 Options.compaction_options_universal.min_merge_width: 2
2025/05/29-10:11:35.720686 178 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/29-10:11:35.720687 178 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/29-10:11:35.720689 178 Options.compaction_options_universal.compression_size_percent: -1
2025/05/29-10:11:35.720691 178 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/29-10:11:35.720693 178 Options.compaction_options_universal.max_read_amp: -1
2025/05/29-10:11:35.720695 178 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/29-10:11:35.720697 178 Options.compaction_options_fifo.allow_compaction: 0
2025/05/29-10:11:35.720701 178                   Options.table_properties_collectors: 
2025/05/29-10:11:35.720702 178                   Options.inplace_update_support: 0
2025/05/29-10:11:35.720704 178                 Options.inplace_update_num_locks: 10000
2025/05/29-10:11:35.720706 178               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/29-10:11:35.720708 178               Options.memtable_whole_key_filtering: 0
2025/05/29-10:11:35.720709 178   Options.memtable_huge_page_size: 0
2025/05/29-10:11:35.720711 178                           Options.bloom_locality: 0
2025/05/29-10:11:35.720712 178                    Options.max_successive_merges: 0
2025/05/29-10:11:35.720713 178             Options.strict_max_successive_merges: 0
2025/05/29-10:11:35.720715 178                Options.optimize_filters_for_hits: 0
2025/05/29-10:11:35.720716 178                Options.paranoid_file_checks: 0
2025/05/29-10:11:35.720718 178                Options.force_consistency_checks: 1
2025/05/29-10:11:35.720719 178                Options.report_bg_io_stats: 0
2025/05/29-10:11:35.720721 178                               Options.ttl: 2592000
2025/05/29-10:11:35.720722 178          Options.periodic_compaction_seconds: 0
2025/05/29-10:11:35.720724 178                        Options.default_temperature: kUnknown
2025/05/29-10:11:35.720725 178  Options.preclude_last_level_data_seconds: 0
2025/05/29-10:11:35.720727 178    Options.preserve_internal_time_seconds: 0
2025/05/29-10:11:35.720728 178                       Options.enable_blob_files: false
2025/05/29-10:11:35.720730 178                           Options.min_blob_size: 0
2025/05/29-10:11:35.720731 178                          Options.blob_file_size: 268435456
2025/05/29-10:11:35.720733 178                   Options.blob_compression_type: NoCompression
2025/05/29-10:11:35.720735 178          Options.enable_blob_garbage_collection: false
2025/05/29-10:11:35.720737 178      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/29-10:11:35.720738 178 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/29-10:11:35.720743 178          Options.blob_compaction_readahead_size: 0
2025/05/29-10:11:35.720744 178                Options.blob_file_starting_level: 0
2025/05/29-10:11:35.720746 178         Options.experimental_mempurge_threshold: 0.000000
2025/05/29-10:11:35.720748 178            Options.memtable_max_range_deletions: 0
2025/05/29-10:11:35.741438 178 DB pointer 0x7be3ef65bc00
