2025/05/29-10:11:35.651398 180 RocksDB version: 9.9.3
2025/05/29-10:11:35.651531 180 Compile date 2024-12-05 01:25:31
2025/05/29-10:11:35.651534 180 DB SUMMARY
2025/05/29-10:11:35.651538 180 Host name (Env):  beb888368ddf
2025/05/29-10:11:35.651540 180 DB Session ID:  Z3WRR5PFQNLDVUSNLKZT
2025/05/29-10:11:35.651580 180 SST files in ./storage/collections/kaizen_documents/0/segments/78ebe74d-4105-4346-95c5-68e8d8d0bbc8 dir, Total Num: 0, files: 
2025/05/29-10:11:35.651583 180 Write Ahead Log file in ./storage/collections/kaizen_documents/0/segments/78ebe74d-4105-4346-95c5-68e8d8d0bbc8: 
2025/05/29-10:11:35.651587 180                         Options.error_if_exists: 0
2025/05/29-10:11:35.651590 180                       Options.create_if_missing: 1
2025/05/29-10:11:35.651594 180                         Options.paranoid_checks: 1
2025/05/29-10:11:35.651606 180             Options.flush_verify_memtable_count: 1
2025/05/29-10:11:35.651609 180          Options.compaction_verify_record_count: 1
2025/05/29-10:11:35.651610 180                               Options.track_and_verify_wals_in_manifest: 0
2025/05/29-10:11:35.651614 180        Options.verify_sst_unique_id_in_manifest: 1
2025/05/29-10:11:35.651616 180                                     Options.env: 0x7be3ef622000
2025/05/29-10:11:35.651619 180                                      Options.fs: PosixFileSystem
2025/05/29-10:11:35.651623 180                                Options.info_log: 0x7be3ee686000
2025/05/29-10:11:35.651627 180                Options.max_file_opening_threads: 16
2025/05/29-10:11:35.651633 180                              Options.statistics: (nil)
2025/05/29-10:11:35.651643 180                               Options.use_fsync: 0
2025/05/29-10:11:35.651659 180                       Options.max_log_file_size: 1048576
2025/05/29-10:11:35.651662 180                  Options.max_manifest_file_size: 1073741824
2025/05/29-10:11:35.651665 180                   Options.log_file_time_to_roll: 0
2025/05/29-10:11:35.651667 180                       Options.keep_log_file_num: 1
2025/05/29-10:11:35.651669 180                    Options.recycle_log_file_num: 0
2025/05/29-10:11:35.651674 180                         Options.allow_fallocate: 1
2025/05/29-10:11:35.651679 180                        Options.allow_mmap_reads: 0
2025/05/29-10:11:35.651683 180                       Options.allow_mmap_writes: 0
2025/05/29-10:11:35.651695 180                        Options.use_direct_reads: 0
2025/05/29-10:11:35.651700 180                        Options.use_direct_io_for_flush_and_compaction: 0
2025/05/29-10:11:35.651702 180          Options.create_missing_column_families: 1
2025/05/29-10:11:35.651703 180                              Options.db_log_dir: 
2025/05/29-10:11:35.651706 180                                 Options.wal_dir: 
2025/05/29-10:11:35.651708 180                Options.table_cache_numshardbits: 6
2025/05/29-10:11:35.651730 180                         Options.WAL_ttl_seconds: 0
2025/05/29-10:11:35.651739 180                       Options.WAL_size_limit_MB: 0
2025/05/29-10:11:35.651743 180                        Options.max_write_batch_group_size_bytes: 1048576
2025/05/29-10:11:35.651750 180             Options.manifest_preallocation_size: 4194304
2025/05/29-10:11:35.651768 180                     Options.is_fd_close_on_exec: 1
2025/05/29-10:11:35.651769 180                   Options.advise_random_on_open: 1
2025/05/29-10:11:35.651773 180                    Options.db_write_buffer_size: 0
2025/05/29-10:11:35.651778 180                    Options.write_buffer_manager: 0x7be3ee604e80
2025/05/29-10:11:35.651779 180           Options.random_access_max_buffer_size: 1048576
2025/05/29-10:11:35.651790 180                      Options.use_adaptive_mutex: 0
2025/05/29-10:11:35.651792 180                            Options.rate_limiter: (nil)
2025/05/29-10:11:35.651794 180     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/05/29-10:11:35.651795 180                       Options.wal_recovery_mode: 0
2025/05/29-10:11:35.651797 180                  Options.enable_thread_tracking: 0
2025/05/29-10:11:35.651798 180                  Options.enable_pipelined_write: 0
2025/05/29-10:11:35.651824 180                  Options.unordered_write: 0
2025/05/29-10:11:35.651826 180         Options.allow_concurrent_memtable_write: 1
2025/05/29-10:11:35.651827 180      Options.enable_write_thread_adaptive_yield: 1
2025/05/29-10:11:35.651830 180             Options.write_thread_max_yield_usec: 100
2025/05/29-10:11:35.651833 180            Options.write_thread_slow_yield_usec: 3
2025/05/29-10:11:35.651870 180                               Options.row_cache: None
2025/05/29-10:11:35.651872 180                              Options.wal_filter: None
2025/05/29-10:11:35.651874 180             Options.avoid_flush_during_recovery: 0
2025/05/29-10:11:35.651875 180             Options.allow_ingest_behind: 0
2025/05/29-10:11:35.651878 180             Options.two_write_queues: 0
2025/05/29-10:11:35.651894 180             Options.manual_wal_flush: 0
2025/05/29-10:11:35.651901 180             Options.wal_compression: 0
2025/05/29-10:11:35.651904 180             Options.background_close_inactive_wals: 0
2025/05/29-10:11:35.651912 180             Options.atomic_flush: 0
2025/05/29-10:11:35.651914 180             Options.avoid_unnecessary_blocking_io: 0
2025/05/29-10:11:35.651951 180             Options.prefix_seek_opt_in_only: 0
2025/05/29-10:11:35.651955 180                 Options.persist_stats_to_disk: 0
2025/05/29-10:11:35.651972 180                 Options.write_dbid_to_manifest: 1
2025/05/29-10:11:35.651973 180                 Options.write_identity_file: 1
2025/05/29-10:11:35.651975 180                 Options.log_readahead_size: 0
2025/05/29-10:11:35.651979 180                 Options.file_checksum_gen_factory: Unknown
2025/05/29-10:11:35.651980 180                 Options.best_efforts_recovery: 0
2025/05/29-10:11:35.651984 180                Options.max_bgerror_resume_count: 2147483647
2025/05/29-10:11:35.651987 180            Options.bgerror_resume_retry_interval: 1000000
2025/05/29-10:11:35.651998 180             Options.allow_data_in_errors: 0
2025/05/29-10:11:35.652009 180             Options.db_host_id: __hostname__
2025/05/29-10:11:35.652010 180             Options.enforce_single_del_contracts: true
2025/05/29-10:11:35.652012 180             Options.metadata_write_temperature: kUnknown
2025/05/29-10:11:35.652015 180             Options.wal_write_temperature: kUnknown
2025/05/29-10:11:35.652024 180             Options.max_background_jobs: 2
2025/05/29-10:11:35.652025 180             Options.max_background_compactions: -1
2025/05/29-10:11:35.652033 180             Options.max_subcompactions: 1
2025/05/29-10:11:35.652052 180             Options.avoid_flush_during_shutdown: 0
2025/05/29-10:11:35.652054 180           Options.writable_file_max_buffer_size: 1048576
2025/05/29-10:11:35.652057 180             Options.delayed_write_rate : 16777216
2025/05/29-10:11:35.652059 180             Options.max_total_wal_size: 0
2025/05/29-10:11:35.652061 180             Options.delete_obsolete_files_period_micros: 180000000
2025/05/29-10:11:35.652064 180                   Options.stats_dump_period_sec: 600
2025/05/29-10:11:35.652066 180                 Options.stats_persist_period_sec: 600
2025/05/29-10:11:35.652067 180                 Options.stats_history_buffer_size: 1048576
2025/05/29-10:11:35.652071 180                          Options.max_open_files: 256
2025/05/29-10:11:35.652072 180                          Options.bytes_per_sync: 0
2025/05/29-10:11:35.652074 180                      Options.wal_bytes_per_sync: 0
2025/05/29-10:11:35.652075 180                   Options.strict_bytes_per_sync: 0
2025/05/29-10:11:35.652077 180       Options.compaction_readahead_size: 2097152
2025/05/29-10:11:35.652078 180                  Options.max_background_flushes: -1
2025/05/29-10:11:35.652082 180 Options.daily_offpeak_time_utc: 
2025/05/29-10:11:35.652083 180 Compression algorithms supported:
2025/05/29-10:11:35.652085 180 	kZSTD supported: 0
2025/05/29-10:11:35.652087 180 	kXpressCompression supported: 0
2025/05/29-10:11:35.652098 180 	kBZip2Compression supported: 0
2025/05/29-10:11:35.652100 180 	kZSTDNotFinalCompression supported: 0
2025/05/29-10:11:35.652105 180 	kLZ4Compression supported: 1
2025/05/29-10:11:35.652108 180 	kZlibCompression supported: 0
2025/05/29-10:11:35.652110 180 	kLZ4HCCompression supported: 1
2025/05/29-10:11:35.652115 180 	kSnappyCompression supported: 1
2025/05/29-10:11:35.652120 180 Fast CRC32 supported: Not supported on x86
2025/05/29-10:11:35.652121 180 DMutex implementation: pthread_mutex_t
2025/05/29-10:11:35.652123 180 Jemalloc supported: 0
2025/05/29-10:11:35.676259 180               Options.comparator: leveldb.BytewiseComparator
2025/05/29-10:11:35.676265 180           Options.merge_operator: None
2025/05/29-10:11:35.676268 180        Options.compaction_filter: None
2025/05/29-10:11:35.676270 180        Options.compaction_filter_factory: None
2025/05/29-10:11:35.676272 180  Options.sst_partitioner_factory: None
2025/05/29-10:11:35.676274 180         Options.memtable_factory: SkipListFactory
2025/05/29-10:11:35.676276 180            Options.table_factory: BlockBasedTable
2025/05/29-10:11:35.676343 180            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7be3ee600ce0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7be3ee62a010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/29-10:11:35.676352 180        Options.write_buffer_size: 10485760
2025/05/29-10:11:35.676355 180  Options.max_write_buffer_number: 2
2025/05/29-10:11:35.676360 180          Options.compression: LZ4
2025/05/29-10:11:35.676362 180                  Options.bottommost_compression: Disabled
2025/05/29-10:11:35.676364 180       Options.prefix_extractor: nullptr
2025/05/29-10:11:35.676366 180   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/29-10:11:35.676368 180             Options.num_levels: 7
2025/05/29-10:11:35.676382 180        Options.min_write_buffer_number_to_merge: 1
2025/05/29-10:11:35.676384 180     Options.max_write_buffer_number_to_maintain: 0
2025/05/29-10:11:35.676386 180     Options.max_write_buffer_size_to_maintain: 0
2025/05/29-10:11:35.676388 180            Options.bottommost_compression_opts.window_bits: -14
2025/05/29-10:11:35.676391 180                  Options.bottommost_compression_opts.level: 32767
2025/05/29-10:11:35.676411 180               Options.bottommost_compression_opts.strategy: 0
2025/05/29-10:11:35.676413 180         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.676415 180         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.676418 180         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/29-10:11:35.676421 180                  Options.bottommost_compression_opts.enabled: false
2025/05/29-10:11:35.676425 180         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.676427 180         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.676429 180            Options.compression_opts.window_bits: -14
2025/05/29-10:11:35.676431 180                  Options.compression_opts.level: 32767
2025/05/29-10:11:35.676433 180               Options.compression_opts.strategy: 0
2025/05/29-10:11:35.676444 180         Options.compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.676446 180         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.676449 180         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.676452 180         Options.compression_opts.parallel_threads: 1
2025/05/29-10:11:35.676457 180                  Options.compression_opts.enabled: false
2025/05/29-10:11:35.676476 180         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.676478 180      Options.level0_file_num_compaction_trigger: 4
2025/05/29-10:11:35.676482 180          Options.level0_slowdown_writes_trigger: 20
2025/05/29-10:11:35.676484 180              Options.level0_stop_writes_trigger: 36
2025/05/29-10:11:35.676488 180                   Options.target_file_size_base: 67108864
2025/05/29-10:11:35.676490 180             Options.target_file_size_multiplier: 1
2025/05/29-10:11:35.676495 180                Options.max_bytes_for_level_base: 268435456
2025/05/29-10:11:35.676497 180 Options.level_compaction_dynamic_level_bytes: 1
2025/05/29-10:11:35.676501 180          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/29-10:11:35.676505 180 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/29-10:11:35.676509 180 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/29-10:11:35.676511 180 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/29-10:11:35.676513 180 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/29-10:11:35.676515 180 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/29-10:11:35.676517 180 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/29-10:11:35.676521 180 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/29-10:11:35.676586 180       Options.max_sequential_skip_in_iterations: 8
2025/05/29-10:11:35.676588 180                    Options.max_compaction_bytes: 1677721600
2025/05/29-10:11:35.676590 180                        Options.arena_block_size: 1048576
2025/05/29-10:11:35.676592 180   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/29-10:11:35.676596 180   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/29-10:11:35.676599 180                Options.disable_auto_compactions: 0
2025/05/29-10:11:35.676602 180                        Options.compaction_style: kCompactionStyleLevel
2025/05/29-10:11:35.676604 180                          Options.compaction_pri: kMinOverlappingRatio
2025/05/29-10:11:35.676606 180 Options.compaction_options_universal.size_ratio: 1
2025/05/29-10:11:35.676608 180 Options.compaction_options_universal.min_merge_width: 2
2025/05/29-10:11:35.676613 180 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/29-10:11:35.676622 180 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/29-10:11:35.676624 180 Options.compaction_options_universal.compression_size_percent: -1
2025/05/29-10:11:35.676627 180 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/29-10:11:35.676630 180 Options.compaction_options_universal.max_read_amp: -1
2025/05/29-10:11:35.676632 180 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/29-10:11:35.676634 180 Options.compaction_options_fifo.allow_compaction: 0
2025/05/29-10:11:35.676641 180                   Options.table_properties_collectors: 
2025/05/29-10:11:35.676643 180                   Options.inplace_update_support: 0
2025/05/29-10:11:35.676665 180                 Options.inplace_update_num_locks: 10000
2025/05/29-10:11:35.676668 180               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/29-10:11:35.676670 180               Options.memtable_whole_key_filtering: 0
2025/05/29-10:11:35.676672 180   Options.memtable_huge_page_size: 0
2025/05/29-10:11:35.676674 180                           Options.bloom_locality: 0
2025/05/29-10:11:35.676676 180                    Options.max_successive_merges: 0
2025/05/29-10:11:35.676678 180             Options.strict_max_successive_merges: 0
2025/05/29-10:11:35.676684 180                Options.optimize_filters_for_hits: 0
2025/05/29-10:11:35.676686 180                Options.paranoid_file_checks: 0
2025/05/29-10:11:35.676701 180                Options.force_consistency_checks: 1
2025/05/29-10:11:35.676703 180                Options.report_bg_io_stats: 0
2025/05/29-10:11:35.676705 180                               Options.ttl: 2592000
2025/05/29-10:11:35.676707 180          Options.periodic_compaction_seconds: 0
2025/05/29-10:11:35.676709 180                        Options.default_temperature: kUnknown
2025/05/29-10:11:35.676711 180  Options.preclude_last_level_data_seconds: 0
2025/05/29-10:11:35.676714 180    Options.preserve_internal_time_seconds: 0
2025/05/29-10:11:35.676718 180                       Options.enable_blob_files: false
2025/05/29-10:11:35.676720 180                           Options.min_blob_size: 0
2025/05/29-10:11:35.676724 180                          Options.blob_file_size: 268435456
2025/05/29-10:11:35.676729 180                   Options.blob_compression_type: NoCompression
2025/05/29-10:11:35.676731 180          Options.enable_blob_garbage_collection: false
2025/05/29-10:11:35.676733 180      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/29-10:11:35.676736 180 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/29-10:11:35.676738 180          Options.blob_compaction_readahead_size: 0
2025/05/29-10:11:35.676740 180                Options.blob_file_starting_level: 0
2025/05/29-10:11:35.676742 180         Options.experimental_mempurge_threshold: 0.000000
2025/05/29-10:11:35.676744 180            Options.memtable_max_range_deletions: 0
2025/05/29-10:11:35.701963 180               Options.comparator: leveldb.BytewiseComparator
2025/05/29-10:11:35.701968 180           Options.merge_operator: None
2025/05/29-10:11:35.701970 180        Options.compaction_filter: None
2025/05/29-10:11:35.701972 180        Options.compaction_filter_factory: None
2025/05/29-10:11:35.701974 180  Options.sst_partitioner_factory: None
2025/05/29-10:11:35.701975 180         Options.memtable_factory: SkipListFactory
2025/05/29-10:11:35.701977 180            Options.table_factory: BlockBasedTable
2025/05/29-10:11:35.702003 180            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7be3ee600ce0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7be3ee62a010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/29-10:11:35.702009 180        Options.write_buffer_size: 10485760
2025/05/29-10:11:35.702011 180  Options.max_write_buffer_number: 2
2025/05/29-10:11:35.702013 180          Options.compression: LZ4
2025/05/29-10:11:35.702014 180                  Options.bottommost_compression: Disabled
2025/05/29-10:11:35.702016 180       Options.prefix_extractor: nullptr
2025/05/29-10:11:35.702017 180   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/29-10:11:35.702022 180             Options.num_levels: 7
2025/05/29-10:11:35.702023 180        Options.min_write_buffer_number_to_merge: 1
2025/05/29-10:11:35.702025 180     Options.max_write_buffer_number_to_maintain: 0
2025/05/29-10:11:35.702026 180     Options.max_write_buffer_size_to_maintain: 0
2025/05/29-10:11:35.702028 180            Options.bottommost_compression_opts.window_bits: -14
2025/05/29-10:11:35.702030 180                  Options.bottommost_compression_opts.level: 32767
2025/05/29-10:11:35.702032 180               Options.bottommost_compression_opts.strategy: 0
2025/05/29-10:11:35.702033 180         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.702035 180         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.702036 180         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/29-10:11:35.702038 180                  Options.bottommost_compression_opts.enabled: false
2025/05/29-10:11:35.702040 180         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.702041 180         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.702043 180            Options.compression_opts.window_bits: -14
2025/05/29-10:11:35.702045 180                  Options.compression_opts.level: 32767
2025/05/29-10:11:35.702046 180               Options.compression_opts.strategy: 0
2025/05/29-10:11:35.702048 180         Options.compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.702049 180         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.702051 180         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.702054 180         Options.compression_opts.parallel_threads: 1
2025/05/29-10:11:35.702056 180                  Options.compression_opts.enabled: false
2025/05/29-10:11:35.702058 180         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.702059 180      Options.level0_file_num_compaction_trigger: 4
2025/05/29-10:11:35.702061 180          Options.level0_slowdown_writes_trigger: 20
2025/05/29-10:11:35.702062 180              Options.level0_stop_writes_trigger: 36
2025/05/29-10:11:35.702064 180                   Options.target_file_size_base: 67108864
2025/05/29-10:11:35.702066 180             Options.target_file_size_multiplier: 1
2025/05/29-10:11:35.702067 180                Options.max_bytes_for_level_base: 268435456
2025/05/29-10:11:35.702069 180 Options.level_compaction_dynamic_level_bytes: 1
2025/05/29-10:11:35.702071 180          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/29-10:11:35.702073 180 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/29-10:11:35.702075 180 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/29-10:11:35.702076 180 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/29-10:11:35.702078 180 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/29-10:11:35.702079 180 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/29-10:11:35.702081 180 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/29-10:11:35.702082 180 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/29-10:11:35.702084 180       Options.max_sequential_skip_in_iterations: 8
2025/05/29-10:11:35.702085 180                    Options.max_compaction_bytes: 1677721600
2025/05/29-10:11:35.702087 180                        Options.arena_block_size: 1048576
2025/05/29-10:11:35.702089 180   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/29-10:11:35.702090 180   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/29-10:11:35.702092 180                Options.disable_auto_compactions: 0
2025/05/29-10:11:35.702094 180                        Options.compaction_style: kCompactionStyleLevel
2025/05/29-10:11:35.702096 180                          Options.compaction_pri: kMinOverlappingRatio
2025/05/29-10:11:35.702097 180 Options.compaction_options_universal.size_ratio: 1
2025/05/29-10:11:35.702099 180 Options.compaction_options_universal.min_merge_width: 2
2025/05/29-10:11:35.702103 180 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/29-10:11:35.702105 180 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/29-10:11:35.702106 180 Options.compaction_options_universal.compression_size_percent: -1
2025/05/29-10:11:35.702108 180 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/29-10:11:35.702110 180 Options.compaction_options_universal.max_read_amp: -1
2025/05/29-10:11:35.702111 180 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/29-10:11:35.702113 180 Options.compaction_options_fifo.allow_compaction: 0
2025/05/29-10:11:35.702117 180                   Options.table_properties_collectors: 
2025/05/29-10:11:35.702119 180                   Options.inplace_update_support: 0
2025/05/29-10:11:35.702122 180                 Options.inplace_update_num_locks: 10000
2025/05/29-10:11:35.702125 180               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/29-10:11:35.702127 180               Options.memtable_whole_key_filtering: 0
2025/05/29-10:11:35.702129 180   Options.memtable_huge_page_size: 0
2025/05/29-10:11:35.702132 180                           Options.bloom_locality: 0
2025/05/29-10:11:35.702134 180                    Options.max_successive_merges: 0
2025/05/29-10:11:35.702136 180             Options.strict_max_successive_merges: 0
2025/05/29-10:11:35.702137 180                Options.optimize_filters_for_hits: 0
2025/05/29-10:11:35.702139 180                Options.paranoid_file_checks: 0
2025/05/29-10:11:35.702141 180                Options.force_consistency_checks: 1
2025/05/29-10:11:35.702143 180                Options.report_bg_io_stats: 0
2025/05/29-10:11:35.702145 180                               Options.ttl: 2592000
2025/05/29-10:11:35.702147 180          Options.periodic_compaction_seconds: 0
2025/05/29-10:11:35.702149 180                        Options.default_temperature: kUnknown
2025/05/29-10:11:35.702150 180  Options.preclude_last_level_data_seconds: 0
2025/05/29-10:11:35.702152 180    Options.preserve_internal_time_seconds: 0
2025/05/29-10:11:35.702153 180                       Options.enable_blob_files: false
2025/05/29-10:11:35.702155 180                           Options.min_blob_size: 0
2025/05/29-10:11:35.702157 180                          Options.blob_file_size: 268435456
2025/05/29-10:11:35.702158 180                   Options.blob_compression_type: NoCompression
2025/05/29-10:11:35.702161 180          Options.enable_blob_garbage_collection: false
2025/05/29-10:11:35.702163 180      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/29-10:11:35.702165 180 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/29-10:11:35.702167 180          Options.blob_compaction_readahead_size: 0
2025/05/29-10:11:35.702169 180                Options.blob_file_starting_level: 0
2025/05/29-10:11:35.702170 180         Options.experimental_mempurge_threshold: 0.000000
2025/05/29-10:11:35.702172 180            Options.memtable_max_range_deletions: 0
2025/05/29-10:11:35.707559 180               Options.comparator: leveldb.BytewiseComparator
2025/05/29-10:11:35.707564 180           Options.merge_operator: None
2025/05/29-10:11:35.707570 180        Options.compaction_filter: None
2025/05/29-10:11:35.707572 180        Options.compaction_filter_factory: None
2025/05/29-10:11:35.707574 180  Options.sst_partitioner_factory: None
2025/05/29-10:11:35.707576 180         Options.memtable_factory: SkipListFactory
2025/05/29-10:11:35.707585 180            Options.table_factory: BlockBasedTable
2025/05/29-10:11:35.707606 180            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7be3ee600ce0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7be3ee62a010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/29-10:11:35.707613 180        Options.write_buffer_size: 10485760
2025/05/29-10:11:35.707615 180  Options.max_write_buffer_number: 2
2025/05/29-10:11:35.707618 180          Options.compression: LZ4
2025/05/29-10:11:35.707620 180                  Options.bottommost_compression: Disabled
2025/05/29-10:11:35.707622 180       Options.prefix_extractor: nullptr
2025/05/29-10:11:35.707625 180   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/29-10:11:35.707628 180             Options.num_levels: 7
2025/05/29-10:11:35.707630 180        Options.min_write_buffer_number_to_merge: 1
2025/05/29-10:11:35.707633 180     Options.max_write_buffer_number_to_maintain: 0
2025/05/29-10:11:35.707635 180     Options.max_write_buffer_size_to_maintain: 0
2025/05/29-10:11:35.707638 180            Options.bottommost_compression_opts.window_bits: -14
2025/05/29-10:11:35.707640 180                  Options.bottommost_compression_opts.level: 32767
2025/05/29-10:11:35.707643 180               Options.bottommost_compression_opts.strategy: 0
2025/05/29-10:11:35.707647 180         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.707661 180         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.707663 180         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/29-10:11:35.707665 180                  Options.bottommost_compression_opts.enabled: false
2025/05/29-10:11:35.707666 180         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.707668 180         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.707669 180            Options.compression_opts.window_bits: -14
2025/05/29-10:11:35.707671 180                  Options.compression_opts.level: 32767
2025/05/29-10:11:35.707672 180               Options.compression_opts.strategy: 0
2025/05/29-10:11:35.707675 180         Options.compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.707677 180         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.707679 180         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.707682 180         Options.compression_opts.parallel_threads: 1
2025/05/29-10:11:35.707684 180                  Options.compression_opts.enabled: false
2025/05/29-10:11:35.707685 180         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.707687 180      Options.level0_file_num_compaction_trigger: 4
2025/05/29-10:11:35.707689 180          Options.level0_slowdown_writes_trigger: 20
2025/05/29-10:11:35.707691 180              Options.level0_stop_writes_trigger: 36
2025/05/29-10:11:35.707692 180                   Options.target_file_size_base: 67108864
2025/05/29-10:11:35.707694 180             Options.target_file_size_multiplier: 1
2025/05/29-10:11:35.707697 180                Options.max_bytes_for_level_base: 268435456
2025/05/29-10:11:35.707698 180 Options.level_compaction_dynamic_level_bytes: 1
2025/05/29-10:11:35.707701 180          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/29-10:11:35.707703 180 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/29-10:11:35.707704 180 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/29-10:11:35.707707 180 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/29-10:11:35.707708 180 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/29-10:11:35.707710 180 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/29-10:11:35.707711 180 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/29-10:11:35.707714 180 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/29-10:11:35.707716 180       Options.max_sequential_skip_in_iterations: 8
2025/05/29-10:11:35.707718 180                    Options.max_compaction_bytes: 1677721600
2025/05/29-10:11:35.707720 180                        Options.arena_block_size: 1048576
2025/05/29-10:11:35.707723 180   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/29-10:11:35.707725 180   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/29-10:11:35.707726 180                Options.disable_auto_compactions: 0
2025/05/29-10:11:35.707729 180                        Options.compaction_style: kCompactionStyleLevel
2025/05/29-10:11:35.707730 180                          Options.compaction_pri: kMinOverlappingRatio
2025/05/29-10:11:35.707733 180 Options.compaction_options_universal.size_ratio: 1
2025/05/29-10:11:35.707737 180 Options.compaction_options_universal.min_merge_width: 2
2025/05/29-10:11:35.707738 180 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/29-10:11:35.707740 180 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/29-10:11:35.707742 180 Options.compaction_options_universal.compression_size_percent: -1
2025/05/29-10:11:35.707744 180 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/29-10:11:35.707746 180 Options.compaction_options_universal.max_read_amp: -1
2025/05/29-10:11:35.707747 180 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/29-10:11:35.707749 180 Options.compaction_options_fifo.allow_compaction: 0
2025/05/29-10:11:35.707753 180                   Options.table_properties_collectors: 
2025/05/29-10:11:35.707756 180                   Options.inplace_update_support: 0
2025/05/29-10:11:35.707758 180                 Options.inplace_update_num_locks: 10000
2025/05/29-10:11:35.707761 180               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/29-10:11:35.707763 180               Options.memtable_whole_key_filtering: 0
2025/05/29-10:11:35.707764 180   Options.memtable_huge_page_size: 0
2025/05/29-10:11:35.707766 180                           Options.bloom_locality: 0
2025/05/29-10:11:35.707769 180                    Options.max_successive_merges: 0
2025/05/29-10:11:35.707770 180             Options.strict_max_successive_merges: 0
2025/05/29-10:11:35.707772 180                Options.optimize_filters_for_hits: 0
2025/05/29-10:11:35.707777 180                Options.paranoid_file_checks: 0
2025/05/29-10:11:35.707778 180                Options.force_consistency_checks: 1
2025/05/29-10:11:35.707780 180                Options.report_bg_io_stats: 0
2025/05/29-10:11:35.707782 180                               Options.ttl: 2592000
2025/05/29-10:11:35.707785 180          Options.periodic_compaction_seconds: 0
2025/05/29-10:11:35.707787 180                        Options.default_temperature: kUnknown
2025/05/29-10:11:35.707789 180  Options.preclude_last_level_data_seconds: 0
2025/05/29-10:11:35.707812 180    Options.preserve_internal_time_seconds: 0
2025/05/29-10:11:35.707814 180                       Options.enable_blob_files: false
2025/05/29-10:11:35.707816 180                           Options.min_blob_size: 0
2025/05/29-10:11:35.707818 180                          Options.blob_file_size: 268435456
2025/05/29-10:11:35.707821 180                   Options.blob_compression_type: NoCompression
2025/05/29-10:11:35.707823 180          Options.enable_blob_garbage_collection: false
2025/05/29-10:11:35.707825 180      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/29-10:11:35.707827 180 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/29-10:11:35.707829 180          Options.blob_compaction_readahead_size: 0
2025/05/29-10:11:35.707830 180                Options.blob_file_starting_level: 0
2025/05/29-10:11:35.707832 180         Options.experimental_mempurge_threshold: 0.000000
2025/05/29-10:11:35.707855 180            Options.memtable_max_range_deletions: 0
2025/05/29-10:11:35.720513 180               Options.comparator: leveldb.BytewiseComparator
2025/05/29-10:11:35.720517 180           Options.merge_operator: None
2025/05/29-10:11:35.720519 180        Options.compaction_filter: None
2025/05/29-10:11:35.720522 180        Options.compaction_filter_factory: None
2025/05/29-10:11:35.720524 180  Options.sst_partitioner_factory: None
2025/05/29-10:11:35.720526 180         Options.memtable_factory: SkipListFactory
2025/05/29-10:11:35.720529 180            Options.table_factory: BlockBasedTable
2025/05/29-10:11:35.720553 180            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7be3ee600ce0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7be3ee62a010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/29-10:11:35.720574 180        Options.write_buffer_size: 10485760
2025/05/29-10:11:35.720577 180  Options.max_write_buffer_number: 2
2025/05/29-10:11:35.720580 180          Options.compression: LZ4
2025/05/29-10:11:35.720582 180                  Options.bottommost_compression: Disabled
2025/05/29-10:11:35.720583 180       Options.prefix_extractor: nullptr
2025/05/29-10:11:35.720597 180   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/29-10:11:35.720599 180             Options.num_levels: 7
2025/05/29-10:11:35.720601 180        Options.min_write_buffer_number_to_merge: 1
2025/05/29-10:11:35.720603 180     Options.max_write_buffer_number_to_maintain: 0
2025/05/29-10:11:35.720604 180     Options.max_write_buffer_size_to_maintain: 0
2025/05/29-10:11:35.720606 180            Options.bottommost_compression_opts.window_bits: -14
2025/05/29-10:11:35.720609 180                  Options.bottommost_compression_opts.level: 32767
2025/05/29-10:11:35.720617 180               Options.bottommost_compression_opts.strategy: 0
2025/05/29-10:11:35.720619 180         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.720620 180         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.720622 180         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/29-10:11:35.720623 180                  Options.bottommost_compression_opts.enabled: false
2025/05/29-10:11:35.720625 180         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.720627 180         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.720629 180            Options.compression_opts.window_bits: -14
2025/05/29-10:11:35.720631 180                  Options.compression_opts.level: 32767
2025/05/29-10:11:35.720632 180               Options.compression_opts.strategy: 0
2025/05/29-10:11:35.720634 180         Options.compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.720635 180         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.720637 180         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.720638 180         Options.compression_opts.parallel_threads: 1
2025/05/29-10:11:35.720640 180                  Options.compression_opts.enabled: false
2025/05/29-10:11:35.720642 180         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.720644 180      Options.level0_file_num_compaction_trigger: 4
2025/05/29-10:11:35.720646 180          Options.level0_slowdown_writes_trigger: 20
2025/05/29-10:11:35.720648 180              Options.level0_stop_writes_trigger: 36
2025/05/29-10:11:35.720649 180                   Options.target_file_size_base: 67108864
2025/05/29-10:11:35.720651 180             Options.target_file_size_multiplier: 1
2025/05/29-10:11:35.720652 180                Options.max_bytes_for_level_base: 268435456
2025/05/29-10:11:35.720654 180 Options.level_compaction_dynamic_level_bytes: 1
2025/05/29-10:11:35.720656 180          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/29-10:11:35.720658 180 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/29-10:11:35.720660 180 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/29-10:11:35.720661 180 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/29-10:11:35.720663 180 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/29-10:11:35.720667 180 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/29-10:11:35.720669 180 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/29-10:11:35.720671 180 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/29-10:11:35.720672 180       Options.max_sequential_skip_in_iterations: 8
2025/05/29-10:11:35.720674 180                    Options.max_compaction_bytes: 1677721600
2025/05/29-10:11:35.720675 180                        Options.arena_block_size: 1048576
2025/05/29-10:11:35.720677 180   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/29-10:11:35.720679 180   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/29-10:11:35.720681 180                Options.disable_auto_compactions: 0
2025/05/29-10:11:35.720683 180                        Options.compaction_style: kCompactionStyleLevel
2025/05/29-10:11:35.720691 180                          Options.compaction_pri: kMinOverlappingRatio
2025/05/29-10:11:35.720692 180 Options.compaction_options_universal.size_ratio: 1
2025/05/29-10:11:35.720695 180 Options.compaction_options_universal.min_merge_width: 2
2025/05/29-10:11:35.720696 180 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/29-10:11:35.720698 180 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/29-10:11:35.720700 180 Options.compaction_options_universal.compression_size_percent: -1
2025/05/29-10:11:35.720701 180 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/29-10:11:35.720703 180 Options.compaction_options_universal.max_read_amp: -1
2025/05/29-10:11:35.720704 180 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/29-10:11:35.720706 180 Options.compaction_options_fifo.allow_compaction: 0
2025/05/29-10:11:35.720710 180                   Options.table_properties_collectors: 
2025/05/29-10:11:35.720712 180                   Options.inplace_update_support: 0
2025/05/29-10:11:35.720713 180                 Options.inplace_update_num_locks: 10000
2025/05/29-10:11:35.720715 180               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/29-10:11:35.720717 180               Options.memtable_whole_key_filtering: 0
2025/05/29-10:11:35.720718 180   Options.memtable_huge_page_size: 0
2025/05/29-10:11:35.720720 180                           Options.bloom_locality: 0
2025/05/29-10:11:35.720726 180                    Options.max_successive_merges: 0
2025/05/29-10:11:35.720728 180             Options.strict_max_successive_merges: 0
2025/05/29-10:11:35.720729 180                Options.optimize_filters_for_hits: 0
2025/05/29-10:11:35.720731 180                Options.paranoid_file_checks: 0
2025/05/29-10:11:35.720732 180                Options.force_consistency_checks: 1
2025/05/29-10:11:35.720734 180                Options.report_bg_io_stats: 0
2025/05/29-10:11:35.720736 180                               Options.ttl: 2592000
2025/05/29-10:11:35.720737 180          Options.periodic_compaction_seconds: 0
2025/05/29-10:11:35.720739 180                        Options.default_temperature: kUnknown
2025/05/29-10:11:35.720741 180  Options.preclude_last_level_data_seconds: 0
2025/05/29-10:11:35.720742 180    Options.preserve_internal_time_seconds: 0
2025/05/29-10:11:35.720743 180                       Options.enable_blob_files: false
2025/05/29-10:11:35.720745 180                           Options.min_blob_size: 0
2025/05/29-10:11:35.720746 180                          Options.blob_file_size: 268435456
2025/05/29-10:11:35.720748 180                   Options.blob_compression_type: NoCompression
2025/05/29-10:11:35.720750 180          Options.enable_blob_garbage_collection: false
2025/05/29-10:11:35.720751 180      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/29-10:11:35.720753 180 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/29-10:11:35.720755 180          Options.blob_compaction_readahead_size: 0
2025/05/29-10:11:35.720756 180                Options.blob_file_starting_level: 0
2025/05/29-10:11:35.720758 180         Options.experimental_mempurge_threshold: 0.000000
2025/05/29-10:11:35.720759 180            Options.memtable_max_range_deletions: 0
2025/05/29-10:11:35.734166 180               Options.comparator: leveldb.BytewiseComparator
2025/05/29-10:11:35.734171 180           Options.merge_operator: None
2025/05/29-10:11:35.734173 180        Options.compaction_filter: None
2025/05/29-10:11:35.734174 180        Options.compaction_filter_factory: None
2025/05/29-10:11:35.734176 180  Options.sst_partitioner_factory: None
2025/05/29-10:11:35.734178 180         Options.memtable_factory: SkipListFactory
2025/05/29-10:11:35.734179 180            Options.table_factory: BlockBasedTable
2025/05/29-10:11:35.734202 180            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7be3ee600ce0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7be3ee62a010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/29-10:11:35.734206 180        Options.write_buffer_size: 10485760
2025/05/29-10:11:35.734208 180  Options.max_write_buffer_number: 2
2025/05/29-10:11:35.734210 180          Options.compression: LZ4
2025/05/29-10:11:35.734211 180                  Options.bottommost_compression: Disabled
2025/05/29-10:11:35.734213 180       Options.prefix_extractor: nullptr
2025/05/29-10:11:35.734214 180   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/29-10:11:35.734216 180             Options.num_levels: 7
2025/05/29-10:11:35.734217 180        Options.min_write_buffer_number_to_merge: 1
2025/05/29-10:11:35.734225 180     Options.max_write_buffer_number_to_maintain: 0
2025/05/29-10:11:35.734227 180     Options.max_write_buffer_size_to_maintain: 0
2025/05/29-10:11:35.734228 180            Options.bottommost_compression_opts.window_bits: -14
2025/05/29-10:11:35.734233 180                  Options.bottommost_compression_opts.level: 32767
2025/05/29-10:11:35.734234 180               Options.bottommost_compression_opts.strategy: 0
2025/05/29-10:11:35.734236 180         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.734238 180         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.734239 180         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/29-10:11:35.734241 180                  Options.bottommost_compression_opts.enabled: false
2025/05/29-10:11:35.734242 180         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.734244 180         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.734246 180            Options.compression_opts.window_bits: -14
2025/05/29-10:11:35.734247 180                  Options.compression_opts.level: 32767
2025/05/29-10:11:35.734249 180               Options.compression_opts.strategy: 0
2025/05/29-10:11:35.734250 180         Options.compression_opts.max_dict_bytes: 0
2025/05/29-10:11:35.734252 180         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/29-10:11:35.734253 180         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/29-10:11:35.734255 180         Options.compression_opts.parallel_threads: 1
2025/05/29-10:11:35.734256 180                  Options.compression_opts.enabled: false
2025/05/29-10:11:35.734258 180         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/29-10:11:35.734259 180      Options.level0_file_num_compaction_trigger: 4
2025/05/29-10:11:35.734261 180          Options.level0_slowdown_writes_trigger: 20
2025/05/29-10:11:35.734263 180              Options.level0_stop_writes_trigger: 36
2025/05/29-10:11:35.734264 180                   Options.target_file_size_base: 67108864
2025/05/29-10:11:35.734266 180             Options.target_file_size_multiplier: 1
2025/05/29-10:11:35.734267 180                Options.max_bytes_for_level_base: 268435456
2025/05/29-10:11:35.734269 180 Options.level_compaction_dynamic_level_bytes: 1
2025/05/29-10:11:35.734271 180          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/29-10:11:35.734273 180 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/29-10:11:35.734275 180 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/29-10:11:35.734276 180 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/29-10:11:35.734278 180 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/29-10:11:35.734279 180 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/29-10:11:35.734281 180 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/29-10:11:35.734286 180 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/29-10:11:35.734288 180       Options.max_sequential_skip_in_iterations: 8
2025/05/29-10:11:35.734290 180                    Options.max_compaction_bytes: 1677721600
2025/05/29-10:11:35.734291 180                        Options.arena_block_size: 1048576
2025/05/29-10:11:35.734293 180   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/29-10:11:35.734294 180   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/29-10:11:35.734296 180                Options.disable_auto_compactions: 0
2025/05/29-10:11:35.734298 180                        Options.compaction_style: kCompactionStyleLevel
2025/05/29-10:11:35.734300 180                          Options.compaction_pri: kMinOverlappingRatio
2025/05/29-10:11:35.734301 180 Options.compaction_options_universal.size_ratio: 1
2025/05/29-10:11:35.734303 180 Options.compaction_options_universal.min_merge_width: 2
2025/05/29-10:11:35.734305 180 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/29-10:11:35.734306 180 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/29-10:11:35.734308 180 Options.compaction_options_universal.compression_size_percent: -1
2025/05/29-10:11:35.734310 180 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/29-10:11:35.734311 180 Options.compaction_options_universal.max_read_amp: -1
2025/05/29-10:11:35.734314 180 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/29-10:11:35.734315 180 Options.compaction_options_fifo.allow_compaction: 0
2025/05/29-10:11:35.734320 180                   Options.table_properties_collectors: 
2025/05/29-10:11:35.734321 180                   Options.inplace_update_support: 0
2025/05/29-10:11:35.734323 180                 Options.inplace_update_num_locks: 10000
2025/05/29-10:11:35.734325 180               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/29-10:11:35.734326 180               Options.memtable_whole_key_filtering: 0
2025/05/29-10:11:35.734328 180   Options.memtable_huge_page_size: 0
2025/05/29-10:11:35.734330 180                           Options.bloom_locality: 0
2025/05/29-10:11:35.734331 180                    Options.max_successive_merges: 0
2025/05/29-10:11:35.734332 180             Options.strict_max_successive_merges: 0
2025/05/29-10:11:35.734334 180                Options.optimize_filters_for_hits: 0
2025/05/29-10:11:35.734335 180                Options.paranoid_file_checks: 0
2025/05/29-10:11:35.734337 180                Options.force_consistency_checks: 1
2025/05/29-10:11:35.734338 180                Options.report_bg_io_stats: 0
2025/05/29-10:11:35.734340 180                               Options.ttl: 2592000
2025/05/29-10:11:35.734341 180          Options.periodic_compaction_seconds: 0
2025/05/29-10:11:35.734343 180                        Options.default_temperature: kUnknown
2025/05/29-10:11:35.734345 180  Options.preclude_last_level_data_seconds: 0
2025/05/29-10:11:35.734346 180    Options.preserve_internal_time_seconds: 0
2025/05/29-10:11:35.734348 180                       Options.enable_blob_files: false
2025/05/29-10:11:35.734349 180                           Options.min_blob_size: 0
2025/05/29-10:11:35.734351 180                          Options.blob_file_size: 268435456
2025/05/29-10:11:35.734352 180                   Options.blob_compression_type: NoCompression
2025/05/29-10:11:35.734354 180          Options.enable_blob_garbage_collection: false
2025/05/29-10:11:35.734356 180      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/29-10:11:35.734358 180 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/29-10:11:35.734360 180          Options.blob_compaction_readahead_size: 0
2025/05/29-10:11:35.734361 180                Options.blob_file_starting_level: 0
2025/05/29-10:11:35.734363 180         Options.experimental_mempurge_threshold: 0.000000
2025/05/29-10:11:35.734364 180            Options.memtable_max_range_deletions: 0
2025/05/29-10:11:35.743478 180 DB pointer 0x7be3ee652c00
