# **命令**

あなたは「Internal Kaizen Assistant」開発プロジェクトのリードAIエンジニアです。  
添付のシステム要件定義資料群（特に、現在右側に表示されている\*\*ika\_system\_overview\_v2\*\*と題された「分割資料1：システム全体概要と開発の基本方針」を含む、分割資料1〜6）と、特に「分割資料1」内の開発方針・アプローチに厳密に従って、システム開発を開始してください。

## MCP(Model Context Protocol)の活用
開発履歴の記録で日付が必要な場合は、time-mcpを使う。最新のライブラリ等は、mcp-deepwikiで正しい使い方を調べる。開発時のLLMに対するメモリーが必要な場合はmemoryを利用する

# **開発の前提条件とAPIコスト削減について**

開発を進める上での基本的な前提条件（使用するプログラミング言語、基本運用環境、開発スタイル、ライブラリのバージョン管理方針など）および、LLMのAPIコストとリクエスト回数を削減するための具体的な指示については、「分割資料1：システム全体概要と開発の基本方針」（ika\_system\_overview\_v2）の「7. 開発方針および考慮事項」に記載されている内容を必ず熟読し、遵守してください。  
特に、一度の指示でより多くの確定的作業を行えるような出力（例：シェルスクリプト生成）、コードのみの出力、適切なコンテキスト参照などを心がけてください。

# **最初のタスク： 開発環境構築スクリプトの生成**

「段階的MVP開発アプローチ」の【ステージ1】に着手します。  
以下の仕様を満たす、開発環境構築用のシェルスクリプト init\_project.sh を1つ生成してください。このスクリプトは、人間が一度実行するだけで、プロジェクトの基本的な骨格が全て構築されるようにしてください。  
**init\_project.sh の仕様:**

1. ディレクトリ構造の構築:  
   以下のディレクトリをmkdir \-pコマンドで一度に作成する。  
   .  
   ├── backend/  
   │   ├── app/  
   │   │   ├── api/  
   │   │   ├── core/  
   │   │   ├── crud/  
   │   │   ├── models/  
   │   │   └── schemas/  
   │   └── tests/  
   └── data/  
       ├── minio/  
       ├── postgres/  
       └── qdrant/

2. ファイルの一括生成:  
   cat \<\< 'EOF' \> \[ファイルパス\] のようなヒアドキュメント形式を用いて、以下の設定ファイルと空のPythonファイルを一括で生成する。  
   * **docker-compose.yml**:  
     * version指定はしない。  
     * サービス（backend, db, qdrant, minio）を定義。  
     * backendサービスでのGPU利用設定を含む。  
     * 設定値は.envファイルから読み込む構成。  
     * データ永続化のためのボリューム設定を含む。  
   * **backend/Dockerfile**:  
     * ベースイメージは python:3.12-slim。  
     * requirements.txt をコピーしてライブラリをインストールする構成。  
   * **backend/requirements.txt**:  
     * バージョン指定なしでライブラリを列挙（fastapi, uvicorn\[standard\], psycopg2-binary, qdrant-client, minio, python-dotenv, sqlalchemy）。  
   * **.env.example**:  
     * 必要な環境変数のテンプレートを記述。  
   * **空のPythonファイル:**  
     * backend/app/main.py  
     * backend/app/api/\_\_init\_\_.py  
     * backend/app/core/\_\_init\_\_.py  
     * backend/app/crud/\_\_init\_\_.py  
     * backend/app/models/\_\_init\_\_.py  
     * backend/app/schemas/\_\_init\_\_.py  
     * backend/tests/\_\_init\_\_.py  
     * （その他、\_\_init\_\_.pyが必要なディレクトリにも配置）

# **成果物**

上記の仕様を全て満たす、単一のシェルスクリプト **init\_project.sh** のコードを生成してください。解説や前置きは不要です。コードのみを提示してください。

# **開発履歴の記録**

このタスクが完了したら、development\_history.mdに最初の記録を行う準備をしてください。  
記録内容は、「開発環境構築のためのDockerfile、docker-compose.yml、requirements.txt、.env.example、および基本的なディレクトリ構造を作成するシェルスクリプトinit\_project.shを生成した」といった内容になります。