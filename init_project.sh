#!/bin/bash

# Internal Kaizen Assistant - Project Initialization Script
# This script creates the complete project structure and configuration files

echo "Initializing Internal Kaizen Assistant project structure..."

# Create directory structure
mkdir -p backend/app/api
mkdir -p backend/app/core
mkdir -p backend/app/crud
mkdir -p backend/app/models
mkdir -p backend/app/schemas
mkdir -p backend/tests
mkdir -p data/minio
mkdir -p data/postgres
mkdir -p data/qdrant

echo "Directory structure created."

# Create docker-compose.yml
cat << 'EOF' > docker-compose.yml
services:
  backend:
    build: ./backend
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - MINIO_ENDPOINT=${MINIO_ENDPOINT}
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - QDRANT_URL=${QDRANT_URL}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      - ./backend:/app
    depends_on:
      - db
      - qdrant
      - minio
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
    ports:
      - "${POSTGRES_PORT:-5432}:5432"

  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "${QDRANT_PORT:-6333}:6333"
    volumes:
      - ./data/qdrant:/qdrant/storage

  minio:
    image: minio/minio:latest
    command: server /data --console-address ":9001"
    environment:
      - MINIO_ROOT_USER=${MINIO_ACCESS_KEY}
      - MINIO_ROOT_PASSWORD=${MINIO_SECRET_KEY}
    ports:
      - "${MINIO_PORT:-9000}:9000"
      - "${MINIO_CONSOLE_PORT:-9001}:9001"
    volumes:
      - ./data/minio:/data
EOF

echo "docker-compose.yml created."

# Create backend/Dockerfile
cat << 'EOF' > backend/Dockerfile
FROM python:3.12-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
EOF

echo "backend/Dockerfile created."

# Create backend/requirements.txt
cat << 'EOF' > backend/requirements.txt
fastapi
uvicorn[standard]
psycopg2-binary
qdrant-client
minio
python-dotenv
sqlalchemy
EOF

echo "backend/requirements.txt created."

# Create .env.example
cat << 'EOF' > .env.example
# Database Configuration
DATABASE_URL=************************************************/kaizen_db
POSTGRES_DB=kaizen_db
POSTGRES_USER=kaizen_user
POSTGRES_PASSWORD=kaizen_password
POSTGRES_PORT=5432

# MinIO Configuration
MINIO_ENDPOINT=minio:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123
MINIO_PORT=9000
MINIO_CONSOLE_PORT=9001

# Qdrant Configuration
QDRANT_URL=http://qdrant:6333
QDRANT_PORT=6333

# Backend Configuration
BACKEND_PORT=8000

# LLM API Keys (Optional - configure as needed)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
EOF

echo ".env.example created."

# Create empty Python files with __init__.py
touch backend/app/main.py
touch backend/app/__init__.py
touch backend/app/api/__init__.py
touch backend/app/core/__init__.py
touch backend/app/crud/__init__.py
touch backend/app/models/__init__.py
touch backend/app/schemas/__init__.py
touch backend/tests/__init__.py

echo "Empty Python files created."

echo "Project initialization completed successfully!"
echo ""
echo "Next steps:"
echo "1. Copy .env.example to .env and configure your environment variables"
echo "2. Run 'docker-compose up --build' to start the development environment"
echo "3. Access the application at http://localhost:8000"
echo "4. Access MinIO console at http://localhost:9001"
echo "5. Access Qdrant dashboard at http://localhost:6333/dashboard"
EOF
