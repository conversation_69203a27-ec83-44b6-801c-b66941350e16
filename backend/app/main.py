"""
Internal Kaizen Assistant - メインアプリケーション
"""
from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from datetime import timedelta

from .core.config import settings
from .core.database import get_db
from .core.security import authenticate_user, create_access_token, get_current_active_user
from .core.init_db import init_database
from .schemas.user import Token, User
from .models.user import User as UserModel
from .api import documents

# FastAPIアプリケーションの作成
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="製造業向け社内ナレッジ活用プラットフォーム"
)

# CORS設定
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 本番環境では適切に制限する
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# APIルーターを追加
app.include_router(documents.router, prefix="/api")


@app.on_event("startup")
async def startup_event():
    """アプリケーション起動時の処理"""
    init_database()


@app.get("/")
async def root():
    """ルートエンドポイント"""
    return {
        "message": f"Welcome to {settings.app_name}",
        "version": settings.app_version,
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """ヘルスチェックエンドポイント"""
    return {"status": "healthy"}


@app.post("/token", response_model=Token)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """ログインしてアクセストークンを取得"""
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}


@app.get("/users/me", response_model=User)
async def read_users_me(current_user: UserModel = Depends(get_current_active_user)):
    """現在のユーザー情報を取得"""
    return current_user


