# Database Configuration
DATABASE_URL=************************************************/kaizen_db
POSTGRES_DB=kaizen_db
POSTGRES_USER=kaizen_user
POSTGRES_PASSWORD=kaizen_password
POSTGRES_PORT=5432

# MinIO Configuration
MINIO_ENDPOINT=minio:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123
MINIO_PORT=9000
MINIO_CONSOLE_PORT=9001

# Qdrant Configuration
QDRANT_URL=http://qdrant:6333
QDRANT_PORT=6333

# Backend Configuration
BACKEND_PORT=8000

# LLM API Keys (Optional - configure as needed)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
