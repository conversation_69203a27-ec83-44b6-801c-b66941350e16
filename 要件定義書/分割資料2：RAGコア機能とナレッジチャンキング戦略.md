### **分割資料2：RAGコア機能とナレッジチャンキング戦略**

【この資料の目的】  
この資料は、システムの核となるドキュメント検索・管理基盤（RAGコア）の機能要件を定義します。ドキュメントの一元管理、高度な検索パイプライン、そして検索精度を左右する重要な要素である「意味的チャンキング戦略」について詳細に記述します。  
**【内容】**

* 2.1. ドキュメント検索・管理基盤 (RAGコア)  
  * 2.1.1. 社内ドキュメントの一元管理  
  * 2.1.2. 高度なドキュメント検索  
  * 2.1.5. 対応ドキュメント形式と意味的チャンキング戦略

#### **2\. 主な機能**

(本セクションの各機能項目は、「分割資料1」の「6. 段階的MVP開発アプローチ」で定義される各ステージにおいて、段階的に実装されます。)

#### **2.1. ドキュメント検索・管理基盤 (RAGコア)**

##### **2.1.1. 社内ドキュメントの一元管理 (MinIO, Docling)**

* **対応ドキュメント形式:** PDF、Word、Excel、PowerPointなど多様な形式。  
* **ドキュメントストレージ:** オンプレミスオブジェクトストレージ（MinIO）にドキュメントをアップロードし、バージョン管理。  
* **ドキュメント解析・構造化:** Doclingを用いて、レイアウト解析、テキスト抽出、テーブル構造認識、OCR処理を行い、構造化データ（DoclingDocument形式）を生成。  
* **メタデータ管理:** 文書ID、作成者、改訂履歴、Doclingによる解析結果等をPostgreSQLに格納。

##### **2.1.2. 高度なドキュメント検索 (Qdrantハイブリッド検索, ruri-v3埋め込み・リランキング, テキスト生成LLM, LangChain)**

* 自然言語による対話型検索インターフェースを提供。  
* **ハイブリッド検索エンジン (Qdrant):**  
  * 日本語埋め込みモデル（cl-nagoya/ruri-v3-310m）で密ベクトルを生成。  
  * キーワード抽出（TF-IDF、BM25等）により疎ベクトルを生成。  
  * Qdrant単体で密ベクトルと疎ベクトルを組み合わせたハイブリッド検索を実行。  
* **マルチモーダル情報検索:** Doclingで分離した図や表にマルチモーダルLLMでキャプションを自動生成し、ベクトル化してQdrantに格納。  
  * **追加UI要件:** 管理者は生成されたキャプションを確認・編集でき、変更はQdrantおよびPostgreSQLに即時反映される機能が必要。  
* **検索結果のリランキング:** Qdrantからの検索結果を、日本語リランキングモデル（cl-nagoya/ruri-v3-reranker-310m）で並べ替え、LLMに渡すコンテキストの質を向上。  
* **LLMによる回答生成 (LangChainオーケストレーション):**  
  * リランキングされた情報をコンテキストとして、テキスト生成LLMが自然な回答を生成。利用するLLMは管理者が設定した選択肢から選ばれる。  
  * LangChain (LCEL) を用いてRAGパイプラインを構築・制御。  
* **参照元表示とプレビュー:** 回答の根拠となったドキュメントチャンクの参照元（ファイル名、ページ番号、MinIOからの一時DLリンク等）を明示。  
* **チャンク調整UI:** 管理者向けに、意味的チャンキングのパラメータを調整し、結果を簡易プレビューできるUIを提供。  
* **ユーザーフィードバック:** RAGの回答が役立ったかを評価できる機能を導入し、フィードバックをPostgreSQLおよびLangMemに記録。

##### **2.1.5. 対応ドキュメント形式と意味的チャンキング戦略**

* **RAGパイプライン対象データ:** Doclingで抽出されたテキスト、テーブルデータ（Markdown等に変換）、マルチモーダルLLMで生成された画像キャプション。  
* **対応ドキュメント形式:** PDF, Word, Excel, PowerPoint, テキスト, Markdown, HTML, 画像ファイル等、Doclingが対応する形式。  
* **意味的チャンキング戦略（ハイブリッドアプローチ）:** 文書の種類や特性に応じ、複数の手法を組み合わせる。Doclingによる文書構造解析結果を最大限活用する。  
  1. **基本方針:** Doclingで抽出された章、節、項、段落などの文書構造をチャンク分割の主要な境界として尊重する。  
  2. **テキスト部分のチャンキング手法（選択的適用）:**  
     * **再帰的チャンク分割 (RecursiveCharacterTextSplitter):** 基本的な分割手法。句読点や改行を区切り文字とし、チャンクサイズとオーバーラップを設定して階層的に分割。  
     * **意味的セグメンテーション (SemanticChunker \- Experimental):** 文の埋め込みベクトルを比較し、意味的類似度が低い箇所をチャンク境界とする。効果を検証しつつ限定的に導入。  
     * **LLM活用型チャンキング（高度なオプション）:** 非常に複雑な文書に対し、LLMに直接チャンク分割を指示する手法。コストと速度が課題のため、適用は慎重に判断。  
  3. **テーブルデータのチャンキング強化（Docling連携）:** テーブルは以下の3形式で情報を処理し、すべてベクトル化して検索対象とする。  
     * テーブル全体のMarkdown表現。  
     * 大規模テーブルの行単位・意味的グループ単位分割（各チャンクにヘッダー情報を付加）。  
     * テーブル内容のLLMによる自然言語の要約文。  
  4. **GraphRAGのためのチャンキング戦略（Neo4j連携）:** エンティティ・リレーションシップ抽出の精度を高めるため、比較的小さなチャンクサイズ（例：300～600トークン）での分割を検討。  
  5. **実験と評価による最適化:** 実際の社内文書を用いて様々なチャンキング手法とパラメータを試し、Langfuse等の評価ツールで客観的なメトリクスに基づき、最適な戦略を継続的に検証・改善する。