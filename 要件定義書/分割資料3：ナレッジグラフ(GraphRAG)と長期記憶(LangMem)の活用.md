### **分割資料3：ナレッジグラフ(GraphRAG)と長期記憶(LangMem)の活用**

【この資料の目的】  
この資料は、単純なドキュメント検索を超え、システムが知識を「構造化」し「記憶」するための高度な機能（GraphRAGとLangMem）について定義します。これにより、知識間の関連性を深く理解し、パーソナライズされた対話を実現する方法を記述します。  
**【内容】**

* 2.5. GraphRAGの思想に基づくナレッジグラフ活用 (採用スキーマ)  
* 2.6. 長期記憶とパーソナライゼーションのための外部メモリーシステム (LangMem)

#### **2.5. GraphRAGの思想に基づくナレッジグラフ活用 (採用スキーマ)**

本システムは、「ナレッジグラフをRAGシステムの中核的な情報源として活用する」という思想を取り入れる。

* **構造化知識の抽出と構築 (LLM \+ Neo4j):** カイゼン文書や会議記録の内容をLLMに入力し、エンティティ（問題、原因、対策、効果、設備など）とリレーションシップを抽出させ、Neo4jナレッジグラフを構築する。  
* **ノードラベル（情報の種類）の例:**  
  * :KaizenReport (カイゼン報告書)  
  * :Meeting (会議記録)  
  * :Problem (問題・課題)  
  * :Cause (原因)  
  * :Solution (対策)  
  * :Knowledge (普遍的な知識・手法)  
  * :Equipment (設備)  
  * :Person (人物)  
  * :Chunk (テキストチャンク)  
* **リレーションシップタイプ（情報の関係性）の例:**  
  * (:Problem)-\[:CAUSED\_BY\]-\>(:Cause)  
  * (:Problem)-\[:ADDRESSED\_BY\]-\>(:Solution)  
  * (:Solution)-\[:APPLIES\_TECHNIQUE\]-\>(:Knowledge)  
  * (:Meeting)-\[:DISCUSSES\]-\>(:Problem)

##### **2.5.1. データ投入戦略**

* **バッチ処理による初期投入:** 既存文書を一括で解析・抽出し、Neo4jにインポートする。  
* **リアルタイム/ニアリアルタイム処理による継続的更新:** 新規ドキュメントのアップロードやユーザーからのフィードバック、LangGraphエージェントの実行結果などを非同期またはリアルタイムに近い形でNeo4jに反映する。  
* **信頼性管理とエンティティリンキング:** 情報源や信頼度スコアをメタデータとして付与し、異なる文書で言及されている同じエンティティを特定してリンクさせる。

##### **2.5.2. データ活用戦略**

* **RAGにおけるコンテキスト拡張:** Qdrantでのベクトル検索と並行してNeo4jを検索し、グラフを辿って広範な関連情報を収集し、LLMに提供するコンテキストの質と量を向上させる。  
* **洞察の発見とパターンの可視化:** Neo4j GDS (Graph Data Science) を活用し、コミュニティ検出、パス分析、中心性分析などを行い、ユーザーに新たな気づきや改善のヒントを提供する。  
* **パーソナライズされた情報推薦:** ユーザーの役割や過去の履歴（LangMemの情報も活用）に基づき、関連性の高い未読の報告書や知識を推薦する。

#### **2.6. 長期記憶とパーソナライゼーションのための外部メモリーシステム (LangMem)**

##### **2.6.1. LangMem導入の目的と概要**

LLMのステートレスな性質を克服するため、LangChainエコシステムのLangMemを外部メモリーシステムとして導入する。

* **目的:**  
  * 複数セッションにまたがる長期的な対話コンテキストの維持。  
  * ユーザーの役割、専門知識、フィードバック等を記憶し、応答を個別最適化する高度なパーソナライゼーション。  
  * LangGraphエージェントが過去の経験から学び、行動を改善するための学習基盤を提供。  
  * 対話を通じて得られる暗黙知の獲得と活用。

##### **2.6.2. LangMemのアーキテクチャと主要機能**

LangMemは、LangChain/LangGraphと連携する軽量なPythonライブラリ。

* **コアメモリーAPI:** 柔軟なストレージバックエンドと連携可能。  
* **メモリー管理ツール:** エージェントが容易にメモリーを読み書きできるツールを提供。  
* **多様なメモリータイプのサポート:**  
  * **意味記憶 (Semantic Memory):** 事実、定義などの普遍的な情報（例：ユーザーの専門分野）。  
  * **エピソード記憶 (Episodic Memory):** 特定の出来事や経験に関する時系列情報（例：過去の対話ログ）。  
  * **手続き記憶 (Procedural Memory):** タスクの実行方法やスキルに関する情報（例：成功したエージェントの思考プロセス）。

##### **2.6.3. Kaizen AssistantにおけるLangMemの活用シナリオ**

* **パーソナライズされたRAG応答:** ユーザーが過去にどの回答に満足したか(エピソード記憶)を基に応答を調整。  
* **LangGraphエージェントの意思決定支援:** 過去の類似案件でのユーザーの反応(エピソード記憶)や専門レベル(意味記憶)を参照し、提案内容を最適化。  
* **継続的な学習ループ:** ユーザーからのフィードバックをLangMemに記録し、将来の行動改善のための学習データとして活用。  
* **中断と再開のサポート:** 長時間の対話でも、保存されたコンテキストを基にスムーズに再開。

##### **2.6.4. データ永続化と既存データベースとの連携方針**

* **ストレージバックエンド:** LangMemのデータは、既存のPostgreSQLに専用スキーマを設けて格納することを基本方針とする。  
* **PostgreSQLスキーマ案（例）:**  
  * langmem\_semantic\_memory テーブル (memory\_id, user\_id, concept\_key, concept\_value, ...)  
  * langmem\_episodic\_memory テーブル (episode\_id, user\_id, session\_id, event\_type, content\_summary, ...)  
  * langmem\_procedural\_memory テーブル (procedure\_id, user\_id, task\_name, successful\_steps, ...)  
* **BaseStore実装案（KaizenAssistantPostgresStore）:** langchain\_community.stores.AsyncPostgresStoreを参考に、aadd, adelete, asearchなどのメソッドを実装する。  
  * asearchでは、クエリに応じて意味記憶、エピソード記憶、手続き記憶を横断的に検索する戦略を検討。  
* **Qdrant/Neo4jとの連携:** 初期段階ではPostgreSQLを中心とし、必要に応じてQdrantやNeo4jをLangMemのBaseStoreから間接的に利用し、高度な検索を補助することを検討。  
* **メモリーのネームスペース化:** ユーザーID等でメモリー空間を分離し、プライバシーを確保する。