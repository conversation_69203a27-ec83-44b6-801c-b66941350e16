### **分割資料5：UI/UXとシステム要件**

【この資料の目的】  
この資料は、システムのユーザーインターフェース（UI/UX）と、機能要件以外の重要なシステム要件（非機能要件、データ要件、連携要件、コンプライアンス要件）を定義します。これにより、ユーザーがどのようにシステムと対話し、システムがどのような品質基準を満たすべきかを明確にします。  
**【内容】**

* 2.4. ユーザーインターフェースとエクスペリエンス (UI/UX)  
*   
  3. システム要件  
  * 3.1. 機能要件  
  * 3.2. 非機能要件  
  * 3.3. データ要件  
  * 3.4. 外部システム連携要件  
  * 3.5. 法的・倫理的・コンプライアンス要件

#### **2.4. ユーザーインターフェースとエクスペリエンス (UI/UX)**

##### **2.4.1. 全体的なUIコンセプトとデザイン原則**

* **ユーザー中心設計:** 対象ユーザーのITスキルや業務内容を考慮し、直感的で効率的な操作が可能なUIを提供。  
* **シンプルさと一貫性:** システム全体のデザイン言語を統一し、学習コストを低減。  
* **タスク指向:** ユーザーが目的を達成しやすいよう、タスクベースのナビゲーションと情報提示を行う。  
* **フィードバックの適時性:** ユーザー操作に対するシステムの応答や処理状況を適時かつ明確にフィードバック。

##### **2.4.2. 主要画面構成と操作フロー**

* **ダッシュボード画面:** 主要機能へのアクセス、通知、最近のアクティビティなどを表示。  
* **ドキュメント検索画面:** 対話型検索インターフェース、検索結果表示、プレビュー機能。  
* **ナレッジ登録画面:** ドキュメントアップロード、会議記録登録、メタデータ入力フォーム。  
* **生産改善支援画面:** 課題入力、エージェントセット選択、改善提案表示・評価インターフェース。  
* **管理機能画面（管理者向け）:**  
  * ユーザー管理、ロール管理。  
  * チャンキングパラメータ調整UI。  
  * **LLMモデル選択・設定UI:** LLMプロバイダー（OpenAI, Gemini等）のAPIキー等を登録し、RAGコア、マルチモーダルLLM、各LangGraphエージェントといった用途ごとに、どのLLMモデルを使用するかをドロップダウンリストで選択・割り当てる機能。

##### **2.4.3. エージェントセット選択・設定インターフェース (LangGraph連携)**

* **事前定義エージェントセットの選択:** 管理者が予め定義した「エージェントセット」（例：「不良原因分析セット」）をリストやカード形式で表示し、ユーザーが選択。  
* **パラメータ入力:** 選択されたセットの実行に必要なパラメータ（例：分析対象の製品コード、期間）を入力するフォームを提供。  
* **実行と進捗表示:** エージェントセットの実行を開始し、処理の進捗状況をリアルタイムまたは定期的に表示。  
* **結果表示とフィードバック:** 最終的な提案や分析結果を分かりやすく表示し、ユーザーからのフィードバックを収集。

##### **2.4.4. アクセシビリティに関する考慮**

* WCAGなどの標準に準拠し、キーボード操作、スクリーンリーダー対応、十分なコントラスト比の確保などに配慮した設計を行う。

#### **3\. システム要件**

##### **3.1. 機能要件**

本ドキュメント群の「主な機能」セクション（分割資料2, 3, 4）に詳述する通り。

##### **3.2. 非機能要件**

* **3.2.1. パフォーマンス要件:**  
  * **初期方針:** 厳密な秒数目標は設定せず、「単独ユーザーの利用において、操作の待ち時間が許容範囲内であること」を目標とする。長時間処理の場合は進捗表示を行う。  
* **3.2.2. スケーラビリティ要件:**  
  * **初期方針:** 「単独または数名のユーザーによる利用」を前提とする。  
  * **将来的な方針:** 将来的にユーザー数やデータ量が増加した際に、水平・垂直スケーリングが可能な構成を意識する。機能拡張性のため、LangGraphベースの改善提案ロジックはモジュール構造とする。  
* **3.2.3. 可用性要件:**  
  * **システム稼働率:** 目標99.5%（計画メンテナンス時間を除く）。  
  * **障害復旧:** 致命的障害は4営業時間以内、一部機能障害は8営業時間以内の復旧を目指す。  
* **3.2.4. セキュリティ要件:**  
  * **初期方針:** 「動作確認に必要な最小限のセキュリティ」を確保。  
  * **認証:** ユーザー名・パスワード認証とパスワードのハッシュ化は必須。総当たり攻撃対策も実装。  
  * **認可:** 初期段階では、システム管理者、ナレッジ登録者、一般利用者の3つの役割を想定し、機能アクセスを制限。  
  * **データ保護:** 通信の暗号化(TLS/SSL)は推奨。LangMemにおけるデータ分離（ユーザーIDに基づくネームスペース化）は初期から実装。  
* **3.2.5. 保守性・運用性要件:**  
  * **初期方針:** 「コードの可読性とモジュール性」を優先。設定値は環境変数(.envファイル)で管理。監視はLangfuseによる基本的なトレースと主要コンポーネントの死活監視程度とする。  
* **3.2.6. ユーザビリティ要件:**  
  * **学習容易性:** 最小限のトレーニングで基本操作を習得できること。  
  * **エラー防止と回復:** 誤操作を犯しにくいインターフェース設計とし、容易に回復できる手段を提供すること。

##### **3.3. データ要件**

* **3.3.1. 取り扱いデータ:**  
  * 社内ドキュメント（技術報告書、作業標準書等）  
  * 生産関連データ（生産実績、不良率等）  
  * 会議・打ち合わせ記録（音声、テキスト、要約等）  
  * ナレッジベースデータ（IE/TPS手法、Qdrant/Neo4j/LangMemのデータ等）  
  * ユーザーデータ（アカウント情報、ロール、ログ等）  
* **3.3.2. データ量と増加予測:**  
  * 初期データ量: 既存社内ドキュメント約10,000～50,000ファイル。  
  * 5年間の運用を想定したキャパシティプランニングを行う。  
* **3.3.3. データ品質とクレンジング:**  
  * 原則として、システムは入力されたデータをそのまま受け入れる。高度なクレンジングは初期スコープ外。  
  * LLMが生成する図表キャプションについては、管理者が確認・編集できる機能を提供。  
* **3.3.4. データストレージ:**  
  * **MinIO:** オリジナルドキュメントファイル。  
  * **PostgreSQL:** 構造化メタデータ、ユーザー情報、設定、ログ、LangMemの主要データストア。  
  * **Qdrant:** ベクトルデータとペイロード。  
  * **Neo4j:** ナレッジグラフデータ。  
* **3.3.5. データセキュリティとアクセス制御 (データレベル):**  
  * MinIO, PostgreSQL, Qdrant, Neo4jの各機能（バケットポリシー、RLS, ロール等）を利用し、データそのものへのアクセスを制御する。LangMemデータもユーザーIDに基づき制御を徹底する。

##### **3.4. 外部システム連携要件**

* **3.4.1. 連携対象システム一覧:**  
  * 利用するLLM/VLMのAPIサービス（OpenAI, Google Gemini, Anthropic Claude, Whisperなど）  
  * OpenRouter (LLM APIゲートウェイサービス、オプション)  
* **3.4.2. 連携方式とインターフェース仕様:**  
  * 各サービスが提供するREST APIまたはSDKを利用。  
* **3.4.3. データ同期頻度とリアルタイム性:**  
  * 各機能利用時にリアルタイム連携。

##### **3.5. 法的・倫理的・コンプライアンス要件**

* **3.5.1. データプライバシーと個人情報保護:**  
  * 取り扱う個人情報（アカウント情報、ドキュメント内容、LangMemの対話履歴等）は、関連法規を遵守して取り扱う。  
* **3.5.2. 著作権と知的財産:**  
  * 外部文献の利用は書誌情報の提示に留め、本文の無断複製や再配布は行わない。  
* **3.5.3. AI倫理と説明責任:**  
  * **バイアスの排除・低減:** LLMに起因するバイアスが不当な影響を与えないよう注意する。  
  * **透明性と説明可能性:** システムが特定の提案に至った根拠を、可能な範囲でユーザーに説明できるようにする（参照元提示、Langfuseトレース、LangMemの関連コンテキスト提示などを活用）。  
  * **人間の監督と最終判断:** AIの提案は支援であり、最終的な意思決定は人間が行うという原則を明確にする。  
  * **誤情報対策:** LLMのハルシネーションリスクを認識し、RAGによる事実に基づいた応答生成や参照元確認を徹底する。