### **分割資料1：システム全体概要と開発の基本方針**

【この資料の目的】  
この資料は、開発対象である「Internal Kaizen Assistant」の全体像、開発の進め方（MVPアプローチ）、AI主導で開発を進める上での基本方針、そして想定される技術アーキテクチャの概要を定義します。開発を担当するLLMは、まずこの資料を読み込み、プロジェクトの目的と全体構造を理解してください。  
**【内容】**

*   
  1. はじめに  
*   
  4. 想定される技術スタック  
*   
  5. システムアーキテクチャ概要とデータフロー  
*   
  6. 段階的MVP開発アプローチ  
*   
  7. 開発方針および考慮事項 (開発の前提条件、APIコスト削減方針を含む)  
*   
  9. 今後の進め方

#### **1\. はじめに**

##### **1.1. システム名称**

Internal Kaizen Assistant

##### **1.2. システム概要**

「Internal Kaizen Assistant」は、製造業の工場運用に特化した社内ナレッジ活用プラットフォームです。最優先事項として、社内に散在する技術文書（PDF、Office文書、図表データ含む）をオンプレミス環境のオブジェクトストレージ（MinIO）で安全に一元管理し、高度なドキュメント解析ツール（Docling）で構造化します。次に、日本語埋め込みモデル（cl-nagoya/ruri-v3-310m）とリランキングモデル（cl-nagoya/ruri-v3-reranker-310m）を活用し、ベクトルデータベース（Qdrant）による高速なハイブリッド検索（密ベクトルと疎ベクトルの組み合わせ）と、テキスト生成LLMによる自然言語応答を組み合わせた高度なRAG（Retrieval-Augmented Generation）機能をLangChainフレームワーク上で提供します。これにより、ユーザーの情報収集活動を強力に支援します。本システムは、ユーザー名とパスワードによる認証機構を提供します。

さらに、IE（インダストリアルエンジニアリング）手法やTPS（トヨタ生産方式）といった専門知識をシステムがサポートし、取り込んだ生産データや抽出したカイゼン事例に基づいて具体的な改善提案を生成することで、生産性向上と安全確保を支援します。この生産改善提案ロジックは、LangGraphを基盤としたマルチエージェントフレームワークを採用し、複数の専門エージェントが協調して動作することで改善提案を生成するアーキテクチャを構築します。

加えて、 LangMem外部メモリーシステムを導入し、LLMの短期記憶の制約を克服します。これにより、複数セッションにまたがる対話履歴、ユーザーごとの思考パターンやフィードバックといったコンテキスト情報を永続的に記憶・活用し、より首尾一貫したインタラクション、高度なパーソナライゼーション、そしてシステム自身の継続的な学習能力を実現します。

また、GraphRAGの思想に基づき、抽出した情報をグラフデータベース（Neo4j）に構造化して格納し、カイゼン事例間の関連性や知識の全体像を把握することで、より深い分析と洞察を可能にします。

本システムは、AI（LLM）がコーディングを行い、開発履歴管理もAIが主導することを基本とし、システム開発者がそれに応じて修正・検証を行うことを前提としています。AIやITの専門家ではない現場の担当者も容易に活用できる直感的なインターフェースを目指します。

##### **1.3. 対象ユーザー**

* 生産技術者  
* 品質管理担当者  
* 現場オペレーター  
* ラインリーダー  
* その他、工場運営に関わる全ての従業員

##### **1.4. 本システムの提供価値（メリット）**

* **情報検索の劇的な高速化と精度向上:** 膨大な社内ドキュメントから必要な情報を瞬時に発見。Qdrantによるハイブリッド検索と ruri-v3-reranker により、高精度な検索を実現。  
* **高度なパーソナライゼーションと文脈理解の向上:** LangMemの導入により、長期的な対話履歴やユーザーの特性を記憶し、個々のユーザーに最適化された対話と支援を提供。  
* **生産改善活動の質の向上と効率化:** IE/TPS等の専門知識に基づいた改善提案を生成。改善提案ロジックはLangGraphベースのモジュールとして設計され、柔軟に更新可能。  
* **ナレッジ共有の促進と属人化の解消:** GraphRAGの思想に基づくナレッジグラフ化により、知識間の関連性を可視化。LangMemによる暗黙知の蓄積と活用も促進。  
* **セキュリティの確保:** 機密性の高い技術文書をオンプレミス環境（MinIO）で安全に管理。  
* **柔軟なアクセス権限管理:** 役割ベースのアクセス制御（RBAC）により、情報セキュリティを担保。  
* **直感的なユーザーインターフェース:** ITスキルを問わず誰でも容易に操作可能。  
* **運用状況の可視化と継続的改善:** システムの利用状況（Langfuseで監視）や改善活動の進捗を把握し、PDCAサイクルを支援。  
* **教育・学習コストの削減と効果向上:** システム利用を通じて、生産改善手法や安全知識を個別最適化された形で習得可能。

#### **4\. 想定される技術スタック**

* **フロントエンド:** React.js, TypeScript  
* **UIライブラリ:** Material-UI (MUI)  
* **状態管理:** Zustand  
* **バックエンドフレームワーク:** FastAPI (Python 3.12)  
* **ドキュメント処理・解析:** Docling  
* **オブジェクトストレージ:** MinIO (オンプレミス)  
* **リレーショナルデータベース:** PostgreSQL (ユーザー情報、メタデータ、システム設定、LangMemデータストア等)  
* **ベクトルデータベース:** Qdrant (ハイブリッド検索)  
* **グラフデータベース:** Neo4j (ナレッジグラフ、GraphRAG思想)  
* **外部メモリーシステム:** LangMem (LangChainの一部)  
  * **ストレージバックエンド連携:** Kaizen Assistant専用のBaseStoreを実装 (AsyncPostgresStore参考)  
* **埋め込みモデル:** cl-nagoya/ruri-v3-310m  
* **リランキングモデル:** cl-nagoya/ruri-v3-reranker-310m  
* **テキスト生成LLM:**  
  * システム管理者がGUIを通じてプロバイダー情報（OpenAI, Gemini, Claude, OpenRouter等）とモデル情報（gpt-4o, gemini-1.5-pro, オンプレミスGemma等）を登録・管理。  
  * RAGコア、マルチモーダルLLM、各LangGraphエージェントなど、用途別に利用するモデルを選択・割り当て。  
* **マルチモーダルLLM:** 管理者が選択したマルチモーダル対応モデルを利用。  
* **RAGオーケストレーションフレームワーク:** LangChain (LCEL)  
* **マルチエージェントフレームワーク:** LangGraph (LangMemと連携)  
* **データ分析・可視化ライブラリ:** Pandas, NumPy, D3.js, Recharts等  
* **LLMトレーシング・監視:** Langfuse  
* **コンテナ実行環境:** Docker, Docker Compose V2  
* **インフラストラクチャ:** オンプレミスサーバー。NVIDIA A4000 16GB相当以上のGPUを初期想定。

#### **5\. システムアーキテクチャ概要とデータフロー (LangGraphベース、LangMem連携)**

##### **主要コンポーネントの役割:**

* **ベクトルデータベース (Qdrant):** テキストチャンク、画像キャプション、テーブル情報の密ベクトルと疎ベクトルを格納し、ハイブリッド検索を実行。  
* **グラフデータベース (Neo4j):** カイゼン文書や会議記録から抽出したエンティティとリレーションをナレッジグラフとして格納。  
* **外部メモリーシステム (LangMem):** エージェントの長期記憶（意味、エピソード、手続き）を管理し、PostgreSQLをバックエンドとして利用。  
* **生産改善提案モジュール (LangGraphベース):** 複数の専門LLMエージェントが協調して提案を生成するグラフ構造のワークフローを実行。  
* **フロントエンド (React.js):** ユーザーインターフェースを提供。  
* **バックエンド (FastAPI):** APIを提供し、ドキュメント処理、DB操作、RAGパイプライン、LangGraphモジュール呼び出し、LangMemアクセスなどの中核ロジックを担う。  
* **その他:** MinIO (オブジェクトストレージ), Docling (ドキュメント解析), ruri-v3モデル (埋め込み/リランキング), PostgreSQL (メタデータ/設定/LangMemストア), LangChain (RAGオーケストレーション), LLM/VLM (言語/画像処理), Langfuse (ログ/監視)。

##### **データフローの概要:**

1. **ドキュメント投入・ナレッジ構築:** ユーザーがドキュメントをアップロード → MinIOに保存 → Doclingで解析 → チャンキング → ベクトル化してQdrantに格納 → LLMでエンティティ・リレーションを抽出しNeo4jに格納。  
2. **意味検索・回答生成 (RAG):** ユーザーが質問 → バックエンドがLangMemで過去の対話等を参照しパーソナライズ → Qdrantでベクトル検索、Neo4jでグラフ検索 → 結果を統合・リランキング → LLMがコンテキストに基づき回答生成 → 対話内容をLangMemに記録。  
3. **生産改善提案生成 (LangGraph):** ユーザーが課題とエージェントセットを選択 → バックエンドが対応するLangGraphグラフを実行 → グラフ内のエージェントがRAG基盤(Qdrant/Neo4j)と長期記憶(LangMem)を参照 → 協調して分析し改善提案を生成 → 実行プロセスや結果をLangMemに記録。

#### **6\. 段階的MVP(Minimum Viable Product)開発アプローチ**

開発は以下の5ステージで段階的に進める。

* **【ステージ1】 コアRAG基盤の確立 (最優先):** 「ドキュメントをアップロードし、自然言語で質問すると、AIが回答する」基本機能を最速で実現する。  
  * **実装機能:** MinIOへのPDFアップロード、Doclingでのテキスト抽出、シンプルな意味的チャンキング、ruri-v3-310mでの密ベクトル生成、Qdrantへの格納、基本的な意味検索、LLMによる回答生成、参照元表示、基本UI、ユーザー認証。  
  * **実装しない機能:** ハイブリッド検索、リランキング、GraphRAG、LangMem、LangGraph、高度な支援機能、詳細な管理UI。  
* **【ステージ2】 RAG基盤の強化とフィードバックループ:** コアRAGの精度とユーザビリティを向上させ、ユーザーからの学習サイクルを確立する。  
  * **実装機能:** リランキングモデル(ruri-v3-reranker)導入、ハイブリッド検索(疎ベクトル)有効化、テーブルチャンキング強化、ユーザーフィードバック機能、LangMemの初期導入（対話履歴の保存・参照）、Langfuse導入、チャンキング調整UIの基本実装。  
* **【ステージ3】 ナレッジグラフ(GraphRAG)の導入:** 知識間の「関係性」を活用する基盤を構築する。  
  * **実装機能:** Neo4j導入、LLMによるエンティティ・リレーション抽出とNeo4jへの格納パイプライン構築、RAGパイプラインを拡張しQdrantとNeo4jの情報を統合利用。  
* **【ステージ4】 長期記憶(LangMem)とパーソナライゼーションの深化:** システムを「個々のユーザーを理解するパートナー」へ進化させる。  
  * **実装機能:** LangMemの本格活用（意味記憶・手続き記憶の導入と自動更新）、蓄積情報に基づくRAG応答のパーソナライズ、教育・学習支援機能の基礎実装。  
* **【ステージ5】 生産改善支援(LangGraph)モジュールの導入:** 全基盤を統合し、「具体的な改善提案」を生成する機能を追加する。  
  * **実装機能:** LangGraph導入、単一目的のエージェントセット（例：「不良原因分析エージェントセット」）の初期実装、エージェントセット選択・実行UIの実装。

#### **7\. 開発方針および考慮事項 (開発の前提条件、APIコスト削減方針を含む)**

本システムは、AI（LLM）がコーディングと開発履歴管理を主導し、開発者は修正・検証を行うプロトタイプとして開発する。

* **開発の前提条件:**  
  * **プログラミング言語:** Python 3.12 を使用します。  
  * **基本運用環境:** Docker Compose を基本とし、各コンポーネント（FastAPIバックエンド、PostgreSQL、Qdrant、MinIO等）は個別のDockerコンテナとして実行します。  
  * **開発スタイル:** サーバー環境を直接汚染せず、開発作業の大部分がDockerコンテナ間で完結するように、ポータブルな環境構築を最優先とします。設定ファイルやデータ永続化についても、コンテナの再起動や再構築で問題が発生しないように考慮します。  
  * **ライブラリのバージョン管理:** Dockerfileのベースイメージ（例: python:3.12-slim）を除き、Pythonライブラリはrequirements.txtにバージョンを指定せず、原則として最新バージョンがインストールされるようにします。これは、LLMのナレッジカットオフによる古いバージョンの指定を避け、互換性問題を低減するためです。  
* **プロトタイプとしての位置づけ:** 機能実現と動作確認を優先し、初期はパフォーマンスやスケーラビリティを最重要視しない。ただし、将来性を考慮した設計を意識する。  
* **LLM(AI)活用の最大化:** コード生成、デバッグ、技術調査、テストコード生成、開発履歴ログ作成においてAIを最大限に活用する。生成されたコードは開発者が責任を持ってレビュー・検証する。  
* **機能のモジュール化:** 生産改善支援機能(2.2)やLLM活用機能(2.3)は、LangGraphベースのアドオン形式の独立モジュールとして設計し、将来の変更やカスタマイズを容易にする。  
* **LLM/VLMモデルの選定と管理:** システム管理者がGUIを通じて、利用可能なLLM/VLMの接続情報（APIキー、モデル名等）を登録し、機能ごとに利用するモデルを選択・設定できる構成とする。オンプレミスLLMもサポートする。  
* **インフラ環境:** GPUが利用可能なDocker Compose環境があれば容易に移植できるよう、ポータビリティを高く保つ。  
* **コア機能への集中:** ステージ1の完遂を最優先目標とする。  
* **意味的チャンキングの重視:** 特に日本語のテーブルデータを含む文書に対し、Doclingの構造解析結果を活用した高度なチャンキング戦略の確立に注力する。  
* **LangGraph/LangMem活用の学習と実践:** 開発チームはこれらの技術の概念を習得し、Langfuse等を活用してデバッグや分析を行う手法を確立する。小規模な実装から始め、実践的なノウハウを蓄積する。  
* **APIコストとリクエスト回数の削減（重要方針）:**  
  * **プロンプトエンジニアリングの工夫:**  
    * **差分（diff）での修正指示の推奨:** ファイル全体を再送するのではなく、変更箇所のみを提示する、またはdiff形式で修正を指示することで、インプットトークンを削減する。  
    * **具体的な出力形式の指定:** 「コードのみ生成」「YAML形式で出力」など、LLMの冗長な解説やテキスト出力を抑制し、アウトプットトークンを削減する。  
    * **コンテキストの参照指示の活用:** 既存資料（本要件定義書、開発履歴など）への参照を促し、プロンプトへの情報重複を避ける。  
  * **タスクの分割とまとめ方の工夫:**  
    * **一度の指示でより多くの確定的作業を行わせる:** mkdir \-pのように、複数のディレクトリやファイルを一度に作成するコマンドや、それらをまとめたシェルスクリプトの生成をLLMに指示することで、対話回数を削減する。  
    * **関連ファイルのバッチ生成:** 関連する複数のファイル（例: モデル、スキーマ、CRUD処理の雛形）の作成を一度のプロンプトで指示する。  
  * **ローカル環境とツールの活用:**  
    * **LLMに依存しない作業の分離:** コードフォーマット（例: black, ruff format）、単純なリファクタリング（IDE機能）、コード検索（IDE機能, grep）などは、ローカルツールやIDEの機能を活用し、LLMへのリクエストを避ける。  
  * **LLMモデルの戦略的な使い分け:**  
    * **タスクに応じたモデル選択:** 複雑な設計やデバッグには高性能モデルを、定型的なコード生成やドキュメント作成には高速・安価なモデル（ローカルOllama含む）を使い分けることで、コストパフォーマンスを最適化する。

##### **7.1. 開発履歴管理（AI主導）**

* **目的:** 開発者のドキュメンテーション負荷を最小限にしつつ、開発の文脈をAIに効率的に引き継がせる。  
* **基本方針:** コーディング作業および開発履歴ログ（development\_history.md）の作成・追記はAIが主導する。開発者はAIが生成したコードやログを確認・修正・検証する。  
* **運用ルール:** 開発者は1日の終わりに、その日の作業内容や重要な出来事を箇条書きでAIに指示するか自ら追記する。AIはそれを基に詳細なログを整形・追記する。開発者はログをレビューし、Gitにコミットする。  
* **記録内容:** 日付、担当者、開発ステージ、達成事項、問題・デバッグ状況、LLMへの指示や気づき、決定事項などを記録する。  
* **ファイル管理:** 開発履歴ファイルはGitで管理し、LLMに開発の続きを依頼する際に、本要件定義書と合わせてインプットとして提供する。

#### **9\. 今後の進め方**

このシステム要件定義書を基に、さらに詳細な議論を進める。特に、以下の項目について詳細を詰めていきたい。

* 意味的チャンキングの具体的なアルゴリズム選定と評価戦略  
* Neo4jのスキーマに基づくデータ投入・活用戦略  
* LangMemのスキーマ設計とBaseStore実装の詳細  
* LangGraphベースの生産改善提案モジュールの初期エージェントセット設計とAPI仕様  
* システム管理者向けLLMモデル選択・設定UIの詳細仕様  
* 各開発ステージにおける詳細なタスク割り当てやスケジュール策定